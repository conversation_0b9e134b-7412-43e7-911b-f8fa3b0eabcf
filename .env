###> symfony/framework-bundle ###
APP_ENV=prod
APP_SECRET=c8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
# DATABASE_URL="sqlite:///%kernel.project_dir%/var/data.db"
# DATABASE_URL="mysql://app:!ChangeMe!@127.0.0.1:3306/app?serverVersion=8.0.32&charset=utf8mb4"
# DATABASE_URL="mysql://app:!ChangeMe!@127.0.0.1:3306/app?serverVersion=10.11.2-MariaDB&charset=utf8mb4"
# DATABASE_URL="postgresql://app:!ChangeMe!@127.0.0.1:5432/app?serverVersion=16&charset=utf8"
###< doctrine/doctrine-bundle ###

###> symfony/messenger ###
# Choose one of the transports below
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
# MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages
MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< symfony/messenger ###

###> symfony/mercure-bundle ###
# See https://symfony.com/doc/current/mercure.html#configuration
# The URL of the Mercure hub, used by the app to publish updates (can be a local URL)
MERCURE_URL=http://mercure/.well-known/mercure
# The public URL of the Mercure hub, used by the browser to connect
MERCURE_PUBLIC_URL=http://localhost:1337/.well-known/mercure
# The secret used to sign the JWTs
MERCURE_JWT_SECRET=SANFEAFcV9JQTKlutJx3cAtudhAGHHkBCSdt3vDxINM=
###< symfony/mercure-bundle ###

# Configuración de Mailer para desarrollo con Gmail SMTP
MAILER_DSN=smtp://<EMAIL>:<EMAIL>:587?encryption=tls&auth_mode=login
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=zyxacjfbfwjntkie
