{"scripts": {"dev": "encore dev", "build": "encore production"}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@popperjs/core": "^2.11.6", "ag-grid-react": "^34.0.0", "apexcharts": "^4.7.0", "bootstrap": "^5.2.3", "core-js": "^3.41.0", "expo-dev-client": "^3.3.8", "expo-notifications": "^0.31.4", "jquery": "^3.7.1", "react-apexcharts": "^1.7.0", "select2": "^4.1.0-rc.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@symfony/webpack-encore": "^5.1.0", "babel-loader": "^10.0.0", "file-loader": "^6.2.0", "webpack": "^5.99.5", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.2"}, "overrides": {"glob": "^10.0.0", "@xmldom/xmldom": "^0.8.0"}}