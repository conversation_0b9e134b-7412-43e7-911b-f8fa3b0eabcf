# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'

    # Note that the session will be started ONLY if you read or write from it.
    session: true

    #esi: true
    #fragments: true

    trusted_hosts: ['^***************$','^localhost$', '^127\.0\.0\.1$', '^sindicato\.grupooptimo\.mx$', '^172\.18\.0\.\d+$','^***************$', '^192\.168\.200\.115$', '^192\.168\.200\.159']
    trusted_proxies: ['127.0.0.1', '10.0.0.0/8', '**********/12', '***********/16']
    trusted_headers: ['x-forwarded-for', 'x-forwarded-host', 'x-forwarded-proto', 'x-forwarded-port', 'x-forwarded-prefix']

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file
