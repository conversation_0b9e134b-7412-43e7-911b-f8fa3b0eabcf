doctrine:
    dbal:
        # default_connection: Master
        connections:
#            default:
#                url: '%env(resolve:DATABASE_URL)%'
            ts:
                url: '%env(resolve:DATABASE_URL_TS)%'
            SNT:
                url: '%env(resolve:DATABASE_URL_SNT)%'
            Master:
                url: '%env(resolve:DATABASE_URL_MASTER)%'

    orm:
        auto_generate_proxy_classes: true
        enable_lazy_ghost_objects: true
        # Configuración para mejorar el manejo de proxies en multi-tenant
        proxy_namespace: 'Proxies'
        # Quitamos auto_mapping: true para configurar entity managers específicos
        #default_entity_manager: Master
        entity_managers:
#            default:
#                connection: default
#                mappings:
#                    App:
#                        type: attribute
#                        is_bundle: false
#                        dir: '%kernel.project_dir%/src/Entity/App'
#                        prefix: 'App\Entity\App'
#                        alias: App
            ts:
                connection: ts
                mappings:
                    App:
                        type: attribute
                        is_bundle: false
                        dir: '%kernel.project_dir%/src/Entity/App'
                        prefix: 'App\Entity\App'
                        alias: App
            SNT:
                connection: SNT
                mappings:
                    App:
                        type: attribute
                        is_bundle: false
                        dir: '%kernel.project_dir%/src/Entity/App'
                        prefix: 'App\Entity\App'
                        alias: App
            Master:
                connection: Master
                mappings:
                    Master:
                        type: attribute
                        is_bundle: false
                        dir: '%kernel.project_dir%/src/Entity/Master'
                        prefix: 'App\Entity\Master'
                        alias: Master
#                    App:
#                        type: attribute
#                        is_bundle: false
#                        dir: '%kernel.project_dir%/src/Entity/App'
#                        prefix: 'App\Entity\App'
#                        alias: App

        controller_resolver:
            auto_mapping: false

when@test:
    doctrine:
        dbal:
            dbname_suffix: '_test%env(default::TEST_TOKEN)%'

when@prod:
    doctrine:
        orm:
            auto_generate_proxy_classes: false
            proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
            query_cache_driver:
                type: pool
                pool: doctrine.system_cache_pool
            result_cache_driver:
                type: pool
                pool: doctrine.result_cache_pool

    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.app
                doctrine.system_cache_pool:
                    adapter: cache.system