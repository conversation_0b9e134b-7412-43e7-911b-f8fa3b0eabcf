# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    uploads_directory: '%kernel.project_dir%/public/uploads'
    social_media_images_directory: '%kernel.project_dir%/public/uploads/social_media'
    app.cache_dir: '%kernel.cache_dir%/tenant_cache'
    app.log_dir: '%kernel.logs_dir%/tenants'
    app.url.dev: '%env(APP_URL_DEV)%'
    app.url.prod: '%env(APP_URL_PROD)%'
    app.env: '%env(APP_ENV)%'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    App\Service\AppUrlService:
        arguments:
            $env: '%app.env%'
            $devUrl: '%app.url.dev%'
            $prodUrl: '%app.url.prod%'
        autowire: false

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    App\Twig\ImagePathExtension:
        arguments:
            $imagePathService: '@App\Service\ImagePathService'
        tags: ['twig.extension']

    # TenantManager Service
    App\Service\TenantManager:
        arguments:
            $doctrine: '@doctrine'
            $requestStack: '@request_stack'

    # TenantRequestSubscriber
    App\EventSubscriber\TenantRequestSubscriber:
        arguments:
            $tenantManager: '@App\Service\TenantManager'
        tags:
            - { name: 'kernel.event_subscriber' }

    # TenantCache Service
    App\Service\TenantCacheService:
        arguments:
            $tenantManager: '@App\Service\TenantManager'
            $logger: '@logger'
            $cacheDir: '%app.cache_dir%'

    # TenantLogger Service
    App\Service\TenantLoggerService:
        arguments:
            $tenantManager: '@App\Service\TenantManager'
            $logDir: '%app.log_dir%'

    # Validation Service
    App\Service\ValidationService:
        arguments:
            $validator: '@validator'
            $serializer: '@serializer'

    # Form Template Service
    App\Service\FormTemplateService:
        arguments:
            $entityManager: '@doctrine.orm.entity_manager'
            $formTemplateRepository: '@App\Repository\FormTemplateRepository'
            $tenantManager: '@App\Service\TenantManager'
            $tenantCache: '@App\Service\TenantCacheService'
            $logger: '@App\Service\TenantLoggerService'

    # Form Field Service
    App\Service\FormFieldService:
        arguments:
            $entityManager: '@doctrine.orm.entity_manager'
            $fieldRepository: '@App\Repository\FormTemplateFieldRepository'
            $logger: '@App\Service\TenantLoggerService'

    # Form Export Service
    App\Service\FormExportService:
        arguments:
            $formTemplateService: '@App\Service\FormTemplateService'
            $formFieldService: '@App\Service\FormFieldService'
            $logger: '@App\Service\TenantLoggerService'

    # Form Template Library Service
    App\Service\FormTemplateLibraryService:
        arguments:
            $entityManager: '@doctrine.orm.entity_manager'
            $tenantManager: '@App\Service\TenantManager'
            $logger: '@App\Service\TenantLoggerService'

    # Form Preview Service
    App\Service\FormPreviewService:
        arguments:
            $formTemplateService: '@App\Service\FormTemplateService'
            $formFieldService: '@App\Service\FormFieldService'

    # Rate Limiter
    App\Security\RateLimiter\TenantRateLimiter:
        arguments:
            $factory: '@limiter.tenant_limiter'
            $tenantManager: '@App\Service\TenantManager'
            $security: '@security.helper'

    # JWT Event Listeners
    App\EventListener\JWTCreatedListener:
        tags:
            - { name: 'kernel.event_listener', event: 'lexik_jwt_authentication.on_jwt_created', method: 'onJWTCreated' }

    App\EventListener\JWTDecodedListener:
        tags:
            - { name: 'kernel.event_listener', event: 'lexik_jwt_authentication.on_jwt_decoded', method: 'onJWTDecoded' }

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    Symfony\Contracts\HttpClient\HttpClientInterface: '@http_client'

    Kreait\Firebase\Messaging: '@kreait_firebase.messaging'

    App\Service\TwilioWhatsAppService:
        arguments:
            $twilioAccountSid: '%env(TWILIO_ACCOUNT_SID)%'
            $twilioAuthToken: '%env(TWILIO_AUTH_TOKEN)%'
            $twilioWhatsAppNumber: '%env(TWILIO_WHATSAPP_NUMBER)%'
            $twilioSMSNumber: '%env(TWILIO_PHONE_NUMBER)%'
            $logger: '@logger'

    App\Service\FallbackNotificationService:
        arguments:
            $logger: '@logger'

    App\Service\ImageUploadService:
        arguments:
            $uploadsDirectory: '%uploads_directory%'

    App\Service\FileUploadService:
        arguments:
            $uploadsDirectory: '%uploads_directory%'

    App\Security\AuthenticationSuccessHandler:
        tags:
            - { name: 'kernel.event_listener', event: 'lexik_jwt_authentication.on_authentication_success', method: 'onAuthenticationSuccess' }

    App\Service\ImagePathService:
        arguments:
            $requestStack: '@request_stack'
            $applicationErrorService: '@App\Service\ApplicationErrorService'
            $params: '@parameter_bag'

    App\Service\PushNotificationService:
        arguments:
            $messaging: '@kreait_firebase.default.messaging'

    App\Service\ExpoNotificationService:
        arguments:
            $expoAccessToken: '%env(EXPO_ACCESS_TOKEN)%'
    # Error Handler Service
    App\Service\ErrorHandlerService:
        arguments:
            $logger: '@logger'
            $tenantManager: '@App\Service\TenantManager'

    # Tenant Exception Subscriber
    App\EventSubscriber\TenantExceptionSubscriber:
        arguments:
            $errorHandler: '@App\Service\ErrorHandlerService'
        tags:
            - { name: 'kernel.event_subscriber' }

    # ErrorLogController
    App\Controller\Api\ErrorLogController:
        arguments:
            $kernelProjectDir: '%kernel.project_dir%'

    App\Service\Auth\CredentialJwtService:
        arguments:
            $secret: '%env(JWT_SECRET_KEY)%'
            $ttl: '%env(int:CREDENTIAL_JWT_TTL)%'

    App\Service\EmailVerificationService:
        arguments:
            $fromAddress: '%env(SMTP_USERNAME)%'

    App\Form\UserAdminType:
        tags: [ 'form.type' ]


    #ajustar
    App\Controller\Api\ConversationController:
        arguments:
            $mercurePublicUrl: '%env(MERCURE_PUBLIC_URL)%'

    App\Service\Auth\MercureJwtGeneratorService:
        arguments:
            $mercureSecret: '%env(MERCURE_JWT_SECRET)%'
