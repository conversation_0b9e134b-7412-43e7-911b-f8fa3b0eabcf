<?php

namespace App\EventSubscriber;

use App\Service\TenantManager;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Twig\Environment;

class TenantRequestSubscriber implements EventSubscriberInterface
{
    private $tenantManager;
    private $twig;

    public function __construct(
        TenantManager $tenantManager,
        Environment $twig)
    {
        $this->tenantManager = $tenantManager;
        $this->twig = $twig;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            // Ejecutar después del enrutamiento (32) pero antes de la autenticación (8)
            KernelEvents::REQUEST => ['onKernelRequest', 31],
        ];
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        $request = $event->getRequest();
        
        // Omitir solicitudes de preflight y rutas que no son de API
        if ($request->isMethod('OPTIONS') || !str_starts_with($request->getPathInfo(), '/')) {
            return;
        }

        // Obtener el tenant de los parámetros de la ruta
        $dominio = $request->attributes->get('dominio');
        
        // Omitir si la ruta no requiere tenant
        if ($dominio === null) {
            return;
        }

        // Validación básica
        if (empty($dominio) || !is_string($dominio)) {
            throw new BadRequestHttpException('El parámetro tenant es inválido');
        }

        try {
            $this->tenantManager->setCurrentTenant($dominio);
        } catch (NotFoundHttpException $e) {
            $response = new Response(
                $this->twig->render('default/index.html.twig', [
                    'dominio' => $dominio
                ]),
                Response::HTTP_NOT_FOUND
            );
            $event->setResponse($response);
            return;
        } catch (\Exception $e) {
            throw new BadRequestHttpException('Error al configurar el tenant: ' . $e->getMessage());
        }
    }
}