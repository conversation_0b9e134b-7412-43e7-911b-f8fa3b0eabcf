<?php
namespace App\Controller\Api;

use App\DTO\EventsGetRequest;
use App\Entity\App\Company;
use App\Entity\App\Event;
use App\Enum\ErrorCodes\Api\EventsErrorCodes;
use App\Enum\Status;
use App\Service\ErrorResponseService;
use App\Service\ImagePathService;
use App\Service\RequestValidatorService;
use App\Service\TenantManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/{dominio}/api')]
class EventsController extends AbstractController
{
    private TenantManager $tenantManager;
    private ErrorResponseService $errorResponseService;
    private RequestValidatorService $requestValidatorService;
    private ImagePathService $imagePathService;

    public function __construct(
        TenantManager $tenantManager,
        ErrorResponseService $errorResponseService,
        RequestValidatorService $requestValidatorService,
        ImagePathService $imagePathService
    ) {
        $this->tenantManager = $tenantManager;
        $this->errorResponseService = $errorResponseService;
        $this->requestValidatorService = $requestValidatorService;
        $this->imagePathService = $imagePathService;
    }


    public function list(string $dominio): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $events = $em->getRepository(Event::class)->findBy([
            'status' => Status::ACTIVE
        ]);

        $data = [];
        foreach ($events as $event) {
            $data[] = [
                'id' => $event->getId(),
                'title' => $event->getTitle(),
                'description' => $event->getDescription(),
                'start_date' => $event->getStartDate()->format('Y-m-d H:i:s'),
                'end_date' => $event->getEndDate()->format('Y-m-d H:i:s'),
                /*'location' => $event->getLocation(),*/
                'image' => $event->getImage(),
                'status' => $event->getStatus()
            ];
        }

        return $this->json($data);
    }

/*    #[Route('/events/{id}', name: 'api_events_show', methods: ['GET'])]
    public function show(string $dominio, int $id): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $event = $em->getRepository(Event::class)->find($id);

        if (!$event) {
            return $this->json(['error' => 'Evento no encontrado'], 404);
        }

        return $this->json([
            'id' => $event->getId(),
            'title' => $event->getTitle(),
            'description' => $event->getDescription(),
            'date' => $event->getDate()->format('Y-m-d H:i:s'),
            'location' => $event->getLocation(),
            'image' => $event->getImage(),
            'active' => $event->isActive()
        ]);
    }

    #[Route('/events', name: 'api_events_create', methods: ['POST'])]
    public function create(string $dominio, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $data = json_decode($request->getContent(), true);

        $event = new Event();
        $event->setTitle($data['title']);
        $event->setDescription($data['description']);
        $event->setDate(new \DateTime($data['date']));
        $event->setLocation($data['location']);
        $event->setImage($data['image'] ?? null);
        $event->setActive($data['active'] ?? true);

        $em->persist($event);
        $em->flush();

        return $this->json([
            'id' => $event->getId(),
            'title' => $event->getTitle(),
            'description' => $event->getDescription(),
            'date' => $event->getDate()->format('Y-m-d H:i:s'),
            'location' => $event->getLocation(),
            'image' => $event->getImage(),
            'active' => $event->isActive()
        ], 201);
    }

    #[Route('/events/{id}', name: 'api_events_update', methods: ['PUT'])]
    public function update(string $dominio, int $id, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $event = $em->getRepository(Event::class)->find($id);

        if (!$event) {
            return $this->json(['error' => 'Evento no encontrado'], 404);
        }

        $data = json_decode($request->getContent(), true);

        if (isset($data['title'])) {
            $event->setTitle($data['title']);
        }
        if (isset($data['description'])) {
            $event->setDescription($data['description']);
        }
        if (isset($data['date'])) {
            $event->setDate(new \DateTime($data['date']));
        }
        if (isset($data['location'])) {
            $event->setLocation($data['location']);
        }
        if (isset($data['image'])) {
            $event->setImage($data['image']);
        }
        if (isset($data['active'])) {
            $event->setActive($data['active']);
        }

        $em->flush();

        return $this->json([
            'id' => $event->getId(),
            'title' => $event->getTitle(),
            'description' => $event->getDescription(),
            'date' => $event->getDate()->format('Y-m-d H:i:s'),
            'location' => $event->getLocation(),
            'image' => $event->getImage(),
            'active' => $event->isActive()
        ]);
    }

    #[Route('/events/{id}', name: 'api_events_delete', methods: ['DELETE'])]
    public function delete(string $dominio, int $id): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $event = $em->getRepository(Event::class)->find($id);

        if (!$event) {
            return $this->json(['error' => 'Evento no encontrado'], 404);
        }

        $em->remove($event);
        $em->flush();

        return $this->json(null, 204);
    }*/
    #[Route('/events', name: 'api_events_list', methods: ['GET'])]
    public function getEvents(string $dominio, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $eventsGetRequest = $this->requestValidatorService->validateAndMap($request, EventsGetRequest::class, true);
        if ($eventsGetRequest instanceof JsonResponse) {
            return $eventsGetRequest;
        }
        
        if (($eventsGetRequest->start_date !== null && $eventsGetRequest->end_date === null) || 
            ($eventsGetRequest->start_date === null && $eventsGetRequest->end_date !== null)) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE_RANGE, 
                [
                    'start_date' => $eventsGetRequest->start_date,
                    'end_date' => $eventsGetRequest->end_date,
                ]
            );
        }

        $company = $em->getRepository(Company::class)->findOneBy([
            'id' => $eventsGetRequest->company_id,
            'status' => Status::ACTIVE
        ]);

        if (!$company) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_COMPANY_NOT_FOUND_OR_INACTIVE, 
                [
                    'company_id' => $eventsGetRequest->company_id,
                ]
            );
        }

        $filteredEvents = $this->filterEvents($em->getRepository(Event::class)->findActiveByCompanies([$company]), $eventsGetRequest);
        if (empty($filteredEvents)) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_NO_EVENTS_FOUND,
                [
                    'company_id' => $eventsGetRequest->company_id,
                    'start_date' => $eventsGetRequest->start_date,
                    'end_date' => $eventsGetRequest->end_date,
                ]
            );
        }

        if ($eventsGetRequest->amount !== null) {
            $filteredEvents = array_slice($filteredEvents, 0, $eventsGetRequest->amount);
        }

        $response = array_map(fn($event) => $this->mapEventToArray($event), $filteredEvents);

        return new JsonResponse([
            'events' => $response,
            'code' => 200,
        ], 200);
    }

    private function filterEvents(Array $events, EventsGetRequest $eventsGetRequest): array
    {
        $filteredEvents = [];

        foreach ($events as $event) {
            if ($event->getStatus() === Status::ACTIVE) {
                $includeEvent = true;

                if ($eventsGetRequest->start_date !== null) {
                    $eventStartDate = $event->getStartDate();
                    $eventEndDate = $event->getEndDate();
                    $startDate = new \DateTime($eventsGetRequest->start_date);
                    $endDate = new \DateTime($eventsGetRequest->end_date);

                    if ($eventStartDate > $endDate || $eventEndDate < $startDate) {
                        $includeEvent = false;
                    }
                }

                if ($includeEvent) {
                    $filteredEvents[] = $event;
                }
            }
        }

        usort($filteredEvents, function ($a, $b) {
            return $a->getStartDate() <=> $b->getStartDate();
        });

        return $filteredEvents;
    }

    private function mapEventToArray($event): array
    {
        return [
            'id' => $event->getId(),
            'title' => $event->getTitle(),
            'description' => $event->getDescription(),
            'start_date' => $event->getStartDate()->format('Y-m-d H:i:s'),
            'end_date' => $event->getEndDate()->format('Y-m-d H:i:s'),
            'image' => $this->imagePathService->generateFullPath($event->getImage()),
        ];
    }
}