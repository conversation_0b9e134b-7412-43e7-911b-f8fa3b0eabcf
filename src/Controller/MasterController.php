<?php

namespace App\Controller;

use App\Entity\Master\Tenant;
use App\Enum\Status;
use App\Form\TenantType;
use App\Service\TenantManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/{dominio}')]
final class MasterController extends AbstractController
{

    public function __construct(
        private readonly TenantManager $tenantManager,
    )
    {
    }

    #[Route('/panel', name: 'app_master', methods: ['GET'])]
    public function index(string $dominio): Response
    {
        $this->tenantManager->clearAllEntityManagers();

        $this->tenantManager->setCurrentTenant($dominio);

        $em = $this->tenantManager->getEntityManager();


        $tenants = $em->getRepository(Tenant::class)->findAll();

        return $this->render('master/index.html.twig', [
            'tenants' => $tenants,
            'dominio' => $dominio,

        ]);
    }

    #[Route('/{rtenant}/edit', name: 'app_master_edit', methods: ['POST', 'GET'])]
    public function editApp(Tenant $rtenant, Request $request, string $dominio): Response
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $tenant = $em->getRepository(Tenant::class)->find($rtenant->getId());

        $form = $this->createForm(TenantType::class, $tenant, [
            'dominio' => $dominio,
            'currentTenant' => $tenant,
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em->flush();
            $this->addFlash('success', 'Tenant successfully updated');
            return $this->redirectToRoute('app_master', ['dominio' => $dominio]);
        }
        return $this->render('master/show.html.twig', [
            'form' => $form,
            'dominio' => $dominio,
            'tenant' => $tenant,
        ]);
    }

    #[Route('/new', name: 'app_master_new', methods: ['GET', 'POST'])]
    public function newApp(Request $request, string $dominio): Response
    {
        $tenant = new Tenant();
        $form = $this->createForm(TenantType::class, $tenant);
        $form->handleRequest($request);

        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        if ($form->isSubmitted() && $form->isValid()) {

            $tenant->setStatus(Status::ACTIVE);

            $em->persist($tenant);
            $em->flush();

            $this->addFlash('success', 'Tenant successfully created');
            return $this->redirectToRoute('app_master', ['dominio' => $dominio]);
        }

        return $this->render('master/show.html.twig', [
            'form' => $form,
            'dominio' => $dominio,
        ]);
    }

    #[Route('/{rtenant}/delete', name: 'app_master_delete', methods: ['POST'])]
    public function deleteApp(Tenant $rtenant, Request $request, string $dominio): Response
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $tenant = $em->getRepository(Tenant::class)->find($rtenant->getId());

        if ($this->isCsrfTokenValid('delete'.$tenant->getId(), $request->request->get('_token'))) {

            if ($tenant->getStatus() === Status::ACTIVE) {
                $tenant->setStatus(Status::INACTIVE);
            } else {
                $tenant->setStatus(Status::ACTIVE);
            }

            $em->flush();
        } else {
            $this->addFlash('error', 'Invalid CSRF token');
        }

        $this->addFlash('success', 'Tenant successfully deleted');
        return $this->redirectToRoute('app_master', ['dominio' => $dominio]);

    }
}
