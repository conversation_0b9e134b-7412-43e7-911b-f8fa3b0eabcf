<?php

namespace App\Controller;

use App\Entity\App\User;
use App\Service\Auth\CredentialJwtService;
use App\Service\TenantManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/{dominio}/credential')]
final class CredentialCheckController extends AbstractController
{

    private CredentialJwtService $credentialJwtService;

    public function __construct(
        private readonly TenantManager $tenantManager,
        CredentialJwtService $credentialJwtService
    ) {
        $this->credentialJwtService = $credentialJwtService;
    }



    #[Route('/check', name: 'app_credential_check', methods: ['GET'])]
    public function check(Request $request, string $dominio, EntityManagerInterface $em)
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            $token = $request->query->get('token');
            if (!$token) {
                return $this->render('credential_check/error.html.twig', [
                    'error' => 'Información no encontrada.',
                ]);
            }

            $payload = $this->credentialJwtService->decodeCredentialToken($token);

            if (!$payload) {
                return $this->render('credential_check/error.html.twig', [
                    'error' => 'La credencia ha vencido, vuelve a generar el código qr desde la app móvil.',
                ]);
            }

            $userId = $payload['sub'] ?? null;

            $user = $em->getRepository(User::class)->find($userId);


            return $this->render('credential_check/index.html.twig', [
                'user' => $user,
            ]);

        } catch (\Throwable $e) {
            return $this->render('credential_check/error.html.twig', [
                'error' => 'Error al verificar la credencial: ' . $e->getMessage(),
            ]);
        }

    }
}
