<?php

namespace App\Controller;

use App\Entity\App\SocialMedia;
use App\Enum\Status;
use App\Form\SocialMediaType;
use App\Repository\SocialMediaRepository;
use App\Service\TenantManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/{dominio}/social-media')]
class SocialMediaController extends AbstractController
{
    private TenantManager $tenantManager;

    public function __construct(TenantManager $tenantManager)
    {
        $this->tenantManager = $tenantManager;
    }

    #[Route('/', name: 'app_social_media_index', methods: ['GET'])]
    public function index(string $dominio, SocialMediaRepository $socialMediaRepository): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('social_media/index.html.twig', [
                'social_medias' => $socialMediaRepository->findAllActive(),
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/new', name: 'app_social_media_new', methods: ['GET', 'POST'])]
    public function new(string $dominio, Request $request, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $socialMedia = new SocialMedia();
            $socialMedia->setCreatedAt(new \DateTime());
            $socialMedia->setUpdatedAt(new \DateTime());
            $socialMedia->setStatus(Status::ACTIVE);
            
            $form = $this->createForm(SocialMediaType::class, $socialMedia);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                /** @var UploadedFile $imageFile */
                $imageFile = $form->get('image')->getData();
                
                if ($imageFile) {
                    $originalFilename = pathinfo($imageFile->getClientOriginalName(), PATHINFO_FILENAME);
                    $safeFilename = transliterator_transliterate('Any-Latin; Latin-ASCII; [^A-Za-z0-9_] remove; Lower()', $originalFilename);
                    $newFilename = $safeFilename.'-'.uniqid().'.'.$imageFile->guessExtension();

                    try {
                        $imageFile->move(
                            $this->getParameter('social_media_images_directory'),
                            $newFilename
                        );
                        $socialMedia->setImage('social_media/' . $newFilename);
                    } catch (FileException $e) {

                    }
                }

                $entityManager->persist($socialMedia);
                $entityManager->flush();

                return $this->redirectToRoute('app_social_media_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
            }

            return $this->render('social_media/new.html.twig', [
                'social_media' => $socialMedia,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}/edit', name: 'app_social_media_edit', methods: ['GET', 'POST'])]
    public function edit(string $dominio, Request $request, SocialMedia $socialMedia, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $form = $this->createForm(SocialMediaType::class, $socialMedia);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                $socialMedia->setUpdatedAt(new \DateTime());
                $entityManager->flush();

                return $this->redirectToRoute('app_social_media_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
            }

            return $this->render('social_media/edit.html.twig', [
                'social_media' => $socialMedia,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}', name: 'app_social_media_show', methods: ['GET'])]
    public function show(string $dominio, SocialMedia $socialMedia): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('social_media/show.html.twig', [
                'social_media' => $socialMedia,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}', name: 'app_social_media_delete', methods: ['POST'])]
    public function delete(string $dominio, Request $request, SocialMedia $socialMedia, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            if ($this->isCsrfTokenValid('delete'.$socialMedia->getId(), $request->getPayload()->getString('_token'))) {
                // Soft delete: cambiar status a INACTIVE en lugar de eliminar físicamente
                $socialMedia->setStatus(Status::INACTIVE);
                $socialMedia->setUpdatedAt(new \DateTime());
                $entityManager->flush();

                $this->addFlash('success', 'Post de redes sociales eliminado correctamente.');
            }

            return $this->redirectToRoute('app_social_media_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }
}
