<?php

namespace App\Controller;

use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;
use App\Service\EmailVerificationService;
use App\Service\TenantManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Session\SessionInterface;

class SecurityController extends AbstractController
{
    private EmailVerificationService $emailVerificationService;
    private TenantManager $tenantManager;

    public function __construct(
        EmailVerificationService $emailVerificationService,
        TenantManager $tenantManager
    ) {
        $this->emailVerificationService = $emailVerificationService;
        $this->tenantManager = $tenantManager;
    }

    #[Route('/{dominio}/login', name: 'app_login')]
    public function login(string $dominio, AuthenticationUtils $authenticationUtils): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            
            $error = $authenticationUtils->getLastAuthenticationError();
            $lastUsername = $authenticationUtils->getLastUsername();

            return $this->render('security/login.html.twig', [
                'last_username' => $lastUsername,
                'error' => $error,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException($e->getMessage());
        }
    }

    #[Route('/{dominio}/logout', name: 'app_logout')]
    public function logout(): void
    {
        throw new \LogicException('Este método es interceptado por el firewall de Symfony.');
    }

    #[Route('/{dominio}/forget-password', name: 'forget_password')]
    public function forgetPassword(
        string $dominio,
        Request $request,
        UserRepository $userRepository,
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $entityManager,
        SessionInterface $session,
        \Psr\Log\LoggerInterface $logger
    ): Response {
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            if ($request->isMethod('GET')) {
                return $this->render('login/forget-password.html.twig', [
                    'dominio' => $dominio
                ]);
            }

            // Si es POST, procesar la solicitud de código
            if ($request->isMethod('POST')) {
                $email = $request->request->get('email');
                $user = $userRepository->findOneBy(['email' => $email]);

                if ($user) {
                    $verificationCode = random_int(100000, 999999);
                    try {
                        $result = $this->emailVerificationService->sendVerificationCode($email, $verificationCode, $dominio);
                        if ($result['success']) {
                            $logger->info("Email enviado a $email con código $verificationCode");
                            $session->set('verification_code', $verificationCode);
                            $session->set('reset_email', $email);
                            return new JsonResponse(['success' => true, 'message' => 'Código enviado correctamente.']);
                        } else {
                            $logger->error("Fallo al enviar email a $email: " . ($result['error'] ?? 'Error desconocido'));
                            return new JsonResponse([
                                'success' => false,
                                'message' => 'Error al enviar el código.',
                                'system_error' => $result['error'] ?? 'Error desconocido',
                                'trace' => $result['trace'] ?? null
                            ], 500);
                        }
                    } catch (\Throwable $e) {
                        $logger->critical("EXCEPCIÓN en email: " . $e->getMessage());
                        return new JsonResponse([
                            'success' => false,
                            'message' => 'Error crítico al enviar el correo: ' . $e->getMessage(),
                            'system_error' => $e->getTraceAsString()
                        ], 500);
                    }
                }

                return new JsonResponse(['success' => false, 'message' => 'Correo electrónico no encontrado.']);
            }

            return new JsonResponse(['success' => false, 'message' => 'Método no permitido.']);
        } catch (\Exception $e) {
            // Mostrar el mensaje de error del sistema en la respuesta JSON para depuración
            return new JsonResponse([
                'success' => false,
                'message' => 'Tenant not found',
                'system_error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    #[Route('/{dominio}/verify-code', name: 'verify_code', methods: ['POST'])]
    public function verifyCode(
        string $dominio,
        Request $request,
        UserRepository $userRepository,
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $entityManager,
        SessionInterface $session
    ): Response {
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            // ✅ Bloqueo de acceso manual:
            if (!$request->isXmlHttpRequest()) {
                return $this->redirectToRoute('app_login', ['dominio' => $dominio]);
            }

            $verificationCode = $request->request->get('verification_code');
            $newPassword = $request->request->get('new_password');
            $storedCode = $session->get('verification_code');
            $email = $session->get('reset_email');

            if (!$storedCode || !$email) {
                return new JsonResponse(['success' => false, 'message' => 'La sesión ha expirado. Por favor, solicite un nuevo código.']);
            }

            if ($verificationCode != $storedCode) {
                return new JsonResponse(['success' => false, 'message' => 'Código de verificación incorrecto.']);
            }

            $user = $userRepository->findOneBy(['email' => $email]);
            if (!$user) {
                return new JsonResponse(['success' => false, 'message' => 'Usuario no encontrado.']);
            }

            // Actualizar la contraseña
            $hashedPassword = $passwordHasher->hashPassword($user, $newPassword);
            $user->setPassword($hashedPassword);

            try {
                $entityManager->persist($user);
                $entityManager->flush();

                // Limpiar la sesión
                $session->remove('verification_code');
                $session->remove('reset_email');

                return new JsonResponse(['success' => true, 'message' => 'Contraseña actualizada correctamente.']);
            } catch (\Exception $e) {
                return new JsonResponse(['success' => false, 'message' => 'Error al actualizar la contraseña.']);
            }
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{dominio}/load-verify-code-template', name: 'load_verify_code_template')]
    public function loadVerifyCodeTemplate(string $dominio): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('login/verify-code.html.twig', [
                'dominio' => $dominio
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }
}
