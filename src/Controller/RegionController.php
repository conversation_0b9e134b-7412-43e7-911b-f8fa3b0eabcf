<?php

namespace App\Controller;

use App\Entity\App\Region;
use App\Enum\Status;
use App\Form\RegionType;
use App\Repository\RegionRepository;
use App\Service\TenantManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/{dominio}/region')]
final class RegionController extends AbstractController
{
    private TenantManager $tenantManager;

    public function __construct(TenantManager $tenantManager)
    {
        $this->tenantManager = $tenantManager;
    }

    #[Route('/', name: 'app_region_index', methods: ['GET'])]
    public function index(string $dominio, RegionRepository $regionRepository): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('region/index.html.twig', [
                'regions' => $regionRepository->findBy(['status' => Status::ACTIVE]),
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/new', name: 'app_region_new', methods: ['GET', 'POST'])]
    public function new(string $dominio, Request $request, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $region = new Region();
            $form = $this->createForm(RegionType::class, $region);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                // Verificar si ya existe una región activa con ese nombre
                $existing = $entityManager->getRepository(Region::class)->findOneBy([
                    'name' => $region->getName(),
                    'status' => Status::ACTIVE
                ]);

                if ($existing) {
                    $this->addFlash('error', 'Ya existe una región con ese nombre.');
                    return $this->redirectToRoute('app_region_new', ['dominio' => $dominio]);
                }

                $region->setStatus(Status::ACTIVE);
                $region->setCreatedAt(new \DateTimeImmutable());
                $region->setUpdatedAt(new \DateTimeImmutable());

                $entityManager->persist($region);
                $entityManager->flush();

                return $this->redirectToRoute('app_region_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
            }

            return $this->render('region/new.html.twig', [
                'region' => $region,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/list', name: 'app_region_list', methods: ['GET'])]
    public function list(string $dominio, RegionRepository $regionRepository): JsonResponse
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $regions = $regionRepository->findBy(['status' => Status::ACTIVE]);

            $data = [];
            foreach ($regions as $region) {
                $data[] = [
                    'id' => $region->getId(),
                    'name' => $region->getName(),
                    'status' => $region->getStatus()->value,
                ];
            }

            return new JsonResponse($data);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}/companies', name: 'app_region_companies', methods: ['GET'])]
    public function getCompaniesByRegion(string $dominio, Region $region): JsonResponse
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $companies = $region->getCompanies();

            $data = [];
            foreach ($companies as $company) {
                if ($company->getStatus() === Status::ACTIVE) {
                    $data[] = [
                        'id' => $company->getId(),
                        'name' => $company->getName(),
                    ];
                }
            }

            return new JsonResponse($data);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}/edit', name: 'app_region_edit', methods: ['GET', 'POST'])]
    public function edit(string $dominio, Request $request, Region $region, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $form = $this->createForm(RegionType::class, $region);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                // Verificar si otra región activa ya tiene ese nombre
                $existing = $entityManager->getRepository(Region::class)->findOneBy([
                    'name' => $region->getName(),
                    'status' => Status::ACTIVE
                ]);

                if ($existing && $existing->getId() !== $region->getId()) {
                    $this->addFlash('error', 'Ya existe otra región activa con ese nombre.');
                    return $this->redirectToRoute('app_region_edit', ['dominio' => $dominio, 'id' => $region->getId()]);
                }

                $region->setUpdatedAt(new \DateTimeImmutable());
                $entityManager->flush();

                return $this->redirectToRoute('app_region_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
            }

            return $this->render('region/edit.html.twig', [
                'region' => $region,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}', name: 'app_region_delete', methods: ['POST'])]
    public function delete(string $dominio, Request $request, Region $region, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            if ($this->isCsrfTokenValid('delete'.$region->getId(), $request->request->get('_token'))) {
                $region->setStatus(Status::INACTIVE);
                $region->setUpdatedAt(new \DateTimeImmutable());

                $entityManager->persist($region);
                $entityManager->flush();
            }

            return $this->redirectToRoute('app_region_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/by-company/{id}', name: 'app_regions_by_company', methods: ['GET'])]
    public function getRegionsByCompany(string $dominio, string $id, RegionRepository $regionRepository): JsonResponse
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $company = $regionRepository->find($id);

            if (!$company) {
                return new JsonResponse(['error' => 'Company not found'], 404);
            }

            $region = $company->getRegion();

            if (!$region) {
                return new JsonResponse(['error' => 'No region found for this company'], 404);
            }

            return new JsonResponse([
                'id' => $region->getId(),
                'name' => $region->getName(),
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}', name: 'app_region_show', methods: ['GET'])]
    public function show(string $dominio, Region $region): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('region/show.html.twig', [
                'region' => $region,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }
}
