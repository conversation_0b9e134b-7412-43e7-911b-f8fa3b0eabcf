<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class DefaultController extends AbstractController
{
    #[Route('/', name: 'app_home')]
    public function home(): Response
    {
        return $this->render('default/index.html.twig', [
            'message' => 'Oops te equivocaste, checa la url',
        ]);
    }

    #[Route('/{dominio}', name: 'app_default')]
    public function index(string $dominio): RedirectResponse
    {
        // Redirigir automáticamente al login del tenant
        return $this->redirectToRoute('app_login', ['dominio' => $dominio]);
    }
}
