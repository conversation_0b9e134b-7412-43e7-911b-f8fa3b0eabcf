<?php

namespace App\Controller;

use App\Entity\App\Beneficiary;
use App\Entity\App\Company;
use App\Entity\App\Region;
use App\Entity\App\Role;
use App\Entity\App\User;
use App\Enum\Status;
use App\Form\UserType;
use App\Repository\CompanyRepository;
use App\Repository\RegionRepository;
use App\Repository\RoleRepository;
use App\Repository\UserRepository;
use App\Service\ImageUploadService;
use App\Service\TenantManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/{dominio}/user')]
final class UserController extends AbstractController
{
    private ImageUploadService $imageUploadService;
    private TenantManager $tenantManager;
    private UserPasswordHasherInterface $passwordHasher;

    public function __construct(ImageUploadService $imageUploadService, TenantManager $tenantManager, UserPasswordHasherInterface $passwordHasher)
    {
        $this->imageUploadService = $imageUploadService;
        $this->tenantManager = $tenantManager;
        $this->passwordHasher = $passwordHasher;
    }

    #[Route('', name: 'app_user_index', methods: ['GET'])]
    public function index(string $dominio, UserRepository $userRepository): Response
    {
        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }
        $this->tenantManager->setCurrentTenant($dominio);

        $em = $this->tenantManager->getEntityManager();

        $userRole = $em->getRepository(Role::class)->findOneBy(['name' => 'ROLE_USER']);

        $users = $userRepository->findBy([
            'status' => Status::ACTIVE,
            'role' => $userRole,
        ]);
        return $this->render('user/index.html.twig', [
            'users' => $users,
            'dominio' => $dominio,
        ]);
    }

    #[Route('/new', name: 'app_user_new', methods: ['GET', 'POST'])]
    public function new(string $dominio, Request $request, EntityManagerInterface $entityManager): Response
    {

        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }

        error_log('Valor de $dominio recibido en new: ' . var_export($dominio, true));
        $this->tenantManager->setCurrentTenant($dominio);
        $user = new User();
        $user->setStatus(Status::INACTIVE); // Usuario inactivo por defecto (status = 0)
        $user->setCreatedAt(new \DateTimeImmutable());
        $user->setUpdatedAt(new \DateTimeImmutable());

        $form = $this->createForm(UserType::class, $user, ['dominio' => $dominio]);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            // Manejar el password si se proporcionó
            $plainPassword = $form->get('password')->getData();
            if ($plainPassword) {
                $hashedPassword = $this->passwordHasher->hashPassword($user, $plainPassword);
                $user->setPassword($hashedPassword);
            }
            $user->setStatus(Status::ACTIVE);
            $user->setVerified(false);

            $entityManager->persist($user);
            $entityManager->flush();
            return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
        }
        return $this->render('user/new.html.twig', [
            'user' => $user,
            'form' => $form,
            'dominio' => $dominio, // <-- Asegura que la variable dominio esté disponible en la vista
        ]);
    }

    #[Route('/{id}', name: 'app_user_show', methods: ['GET'])]
    public function show(string $dominio, User $user): Response
    {
        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $em = $this->tenantManager->getEntityManager();

            $beneficiaries = $em->getRepository(Beneficiary::class)->findBy([
                'user' => $user->getId(),
                'status' => Status::ACTIVE,
            ]);

            return $this->render('user/show.html.twig', [
                'user' => $user,
                'dominio' => $dominio,
                'beneficiaries' => $beneficiaries,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}/edit', name: 'app_user_edit', methods: ['GET', 'POST'])]
    public function edit(string $dominio, Request $request, User $user, EntityManagerInterface $entityManager): Response
    {
        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            $form = $this->createForm(UserType::class, $user, [
                'dominio' => $dominio,
            ]);

            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                // Manejar el password si se proporcionó
                $plainPassword = $form->get('password')->getData();
                if ($plainPassword) {
                    $hashedPassword = $this->passwordHasher->hashPassword($user, $plainPassword);
                    $user->setPassword($hashedPassword);
                }

                $entityManager->flush();

                return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
            }

            return $this->render('user/edit.html.twig', [
                'user' => $user,
                'form' => $form,
                'dominio' => $dominio,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}', name: 'app_user_delete', methods: ['POST'])]
    public function delete(string $dominio, Request $request, User $user, EntityManagerInterface $entityManager): Response
    {
        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            if ($this->isCsrfTokenValid('delete'.$user->getId(), $request->request->get('_token'))) {
                $user->setStatus(Status::INACTIVE);
                $entityManager->flush();
            }

            return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/download-template', name: 'app_user_download_template', priority: 10)]
    public function downloadTemplate(): StreamedResponse
    {
        try {
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->fromArray([
                ['NOMBRE', 'APELLIDOS', 'EMPRESA', 'FECHA DE NACIMIENTO', 'TELÉFONO', 'CORREO ELECTRÓNICO', 'N° DE EMPLEADO', 'CURP', 'GENERO', 'EDUCACIÓN']
            ], null, 'A1');

            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

            $response = new StreamedResponse(function () use ($writer) {
                $writer->save('php://output');
            });

            $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            $response->headers->set('Content-Disposition', 'attachment;filename="plantilla_usuarios.xlsx"');

            return $response;
        } catch (\Exception $e) {
            // Log the error
            error_log('Error generating Excel template: ' . $e->getMessage());

            // Create a simple CSV file as fallback
            $response = new StreamedResponse(function () {
                $output = fopen('php://output', 'w');
                fputcsv($output, ['NOMBRE', 'APELLIDOS', 'EMPRESA', 'FECHA DE NACIMIENTO', 'TELÉFONO', 'CORREO ELECTRÓNICO', 'N° DE EMPLEADO', 'CURP', 'GENERO', 'EDUCACIÓN']);
                fclose($output);
            });

            $response->headers->set('Content-Type', 'text/csv');
            $response->headers->set('Content-Disposition', 'attachment;filename="plantilla_usuarios.csv"');

            return $response;
        }
    }

    #[Route('/bulk-upload', name: 'app_user_bulk_upload', methods: ['POST'], priority: 10)]
    public function bulkUpload(Request $request, EntityManagerInterface $em, CompanyRepository $companyRepo, RoleRepository $roleRepo, RegionRepository $regionRepo): RedirectResponse
    {
        $dominio = $request->attributes->get('dominio');
        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }
        try {
            $this->tenantManager->setCurrentTenant($dominio);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Tenant no encontrado o inválido: ' . $dominio);
            return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
        }

        try {
            $file = $request->files->get('excel_file');
            if (!$file) {
                $this->addFlash('error', 'No se ha subido ningún archivo.');
                return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
            }

            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file->getPathname());
            $sheet = $spreadsheet->getActiveSheet();
            $allRows = $sheet->toArray();
            $headerRow = $allRows[0] ?? [];

            $rows = [$headerRow];
            $rowMapping = [0];
            foreach (array_slice($allRows, 1) as $index => $row) {
                if (array_filter($row, fn($cell) => !empty(trim((string)$cell)))) {
                    $rows[] = $row;
                    $rowMapping[] = $index + 1;
                }
            }

            if (count($rows) <= 1) {
                $this->addFlash('error', 'El archivo no contiene datos para importar.');
                return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
            }

            $defaultCompany = $companyRepo->findOneBy([]);
            $roleUser = $roleRepo->findOneBy(['name' => 'ROLE_USER']) ?? $roleRepo->find(1);
            if (!$roleUser) {
                $this->addFlash('error', 'No se encontró el rol necesario para los usuarios.');
                return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
            }

            $usersAdded = 0;
            $errors = [];
            $regionCache = [];
            $companyCache = [];

            foreach (array_slice($rows, 1) as $index => $row) {
                $originalRowNumber = $rowMapping[$index + 1] + 1;
                $rowNumber = $originalRowNumber + 1;

                if (count($row) < 10) {
                    $errors[] = "Fila $rowNumber: No tiene suficientes columnas.";
                    continue;
                }

                [$nombre, $apellidos, $empresa, $fechaNacimiento, $telefono, $email, $numEmpleado, $curp, $genero, $educacion, $regionUploaded] = $row;

                if (empty(trim((string)$nombre)) || empty(trim((string)$apellidos))) {
                    $errors[] = "Fila $rowNumber: Datos insuficientes. Se requiere al menos nombre y apellidos.";
                    continue;
                }

                // --- Región con caché ---
                $regionKey = strtolower(trim($regionUploaded));
                if (!isset($regionCache[$regionKey])) {
                    $region = $regionRepo->findOneBy(['name' => $regionUploaded, 'status' => Status::ACTIVE]);
                    if (!$region) {
                        $region = new Region();
                        $region->setName($regionUploaded);
                        $region->setStatus(Status::ACTIVE);
                        $region->setCreatedAt(new \DateTime());
                        $region->setUpdatedAt(new \DateTime());
                        $em->persist($region);
                    }
                    $regionCache[$regionKey] = $region;
                }
                $region = $regionCache[$regionKey];

                $empresaKey = strtolower(trim($empresa));
                if (!isset($companyCache[$empresaKey])) {
                    $company = null;
                    if (!empty($empresa)) {
                        $company = $companyRepo->findOneBy(['name' => $empresa]);
                        if (!$company) {
                            $company = new Company();
                            $company->setName($empresa);
                            $company->setStatus(Status::ACTIVE);
                            $company->setRegion($region);
                            $company->setCreatedAt(new \DateTime());
                            $company->setUpdatedAt(new \DateTime());
                            $em->persist($company);
                        }
                    }

                    // Si sigue siendo null, usamos la por defecto (por fallback)
                    if (!$company) {
                        $company = $defaultCompany;
                    }

                    $companyCache[$empresaKey] = $company;
                }
                $company = $companyCache[$empresaKey];


                if (!$company) {
                    $errors[] = "Fila $rowNumber: No se encontró la empresa '$empresa' y no hay empresa por defecto.";
                    continue;
                }

                try {
                    $user = new User();
                    $user->setName(trim((string)$nombre));
                    $user->setLastName(trim((string)$apellidos));

                    $emailValue = trim((string)($email ?? ''));
                    if ($emailValue) {
                        $existingUser = $em->getRepository(User::class)->findOneBy(['email' => $emailValue]);
                        if ($existingUser) {
                            $errors[] = "Fila $rowNumber: Ya existe un usuario con el correo '$emailValue'.";
                            continue;
                        }
                        $user->setEmail($emailValue);
                    } else {
                        $user->setEmail('user_' . uniqid() . '@placeholder.com');
                    }

                    $user->setPhoneNumber(trim((string)($telefono ?? '')));
                    $user->setCurp(trim((string)($curp ?? '')));
                    $user->setEmployeeNumber(trim((string)($numEmpleado ?? '')));
                    $user->setGender(trim((string)($genero ?? '')));
                    $user->setEducation(trim((string)($educacion ?? '')));
                    $user->setCompany($company);
                    $user->addRegion($region);
                    $user->setRole($roleUser);

                    if (!empty($fechaNacimiento)) {
                        try {
                            $fecha = \DateTime::createFromFormat('Y-m-d', $fechaNacimiento)
                                ?: \DateTime::createFromFormat('d/m/Y', $fechaNacimiento)
                                    ?: new \DateTime($fechaNacimiento);
                            $user->setBirthday($fecha);
                        } catch (\Exception $e) {
                            $errors[] = "Fila $rowNumber: Formato de fecha inválido.";
                        }
                    }

                    $user->setStatus(Status::ACTIVE);
                    $user->setCreatedAt(new \DateTimeImmutable());
                    $user->setUpdatedAt(new \DateTimeImmutable());

                    $em->persist($user);
                    $usersAdded++;
                } catch (\Exception $e) {
                    $errors[] = "Fila $rowNumber: Error al crear el usuario: " . $e->getMessage();
                }
            }

            if ($usersAdded > 0) {
                $em->flush();
                $this->addFlash('success', "Se han importado $usersAdded usuarios correctamente.");
            } else {
                $this->addFlash('warning', "No se importaron usuarios. Verifique los datos del archivo.");
            }

            if (!empty($errors)) {
                $this->addFlash('warning', "Se encontraron algunos errores durante la importación:");
                foreach ($errors as $error) {
                    $this->addFlash('warning', $error);
                }
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error al procesar el archivo: ' . $e->getMessage());
            error_log('Error al procesar el archivo: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
        }

        return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
    }

}
