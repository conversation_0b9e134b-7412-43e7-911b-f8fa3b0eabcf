<?php

namespace App\Controller;
use App\Entity\App\Beneficiary;
use App\Entity\App\User;
use App\Enum\Status;
use App\Form\BeneficiaryType;
use App\Repository\BeneficiaryRepository;
use App\Service\ImageUploadService;
use App\Service\TenantManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/{dominio}/beneficiary')]
final class BeneficiaryController extends AbstractController
{
    private ImageUploadService $imageUploadService;
    private TenantManager $tenantManager;

    public function __construct(ImageUploadService $imageUploadService, TenantManager $tenantManager)
    {
        $this->imageUploadService = $imageUploadService;
        $this->tenantManager = $tenantManager;
    }

    #[Route('/', name: 'app_beneficiary_index', methods: ['GET'])]
    public function index(string $dominio, BeneficiaryRepository $beneficiaryRepository): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('beneficiary/index.html.twig', [
                'beneficiaries' => $beneficiaryRepository->findAll(),
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/new', name: 'app_beneficiary_new', methods: ['GET', 'POST'])]
    public function new(string $dominio, Request $request, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $userId = $request->query->get('user_id');
            $user = $entityManager->getRepository(User::class)->find($userId);

            if (!$user) {
                throw $this->createNotFoundException('Usuario no encontrado');
            }

            $beneficiary = new Beneficiary();

            $form = $this->createForm(BeneficiaryType::class, $beneficiary, [
                'user_name' => $user->getName() . ' ' . $user->getLastName(),
            ]);

            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {

                $beneficiary->setUser($user);
                $beneficiary->setStatus(Status::ACTIVE);
                $beneficiary->setCreatedAt(new \DateTimeImmutable());
                $beneficiary->setUpdatedAt(new \DateTimeImmutable());

                /** @var UploadedFile $imageFile */
                $imageFile = $form->get('photo')->getData();
                if ($imageFile) {
                    $relativePath = $this->imageUploadService->uploadImage($imageFile, 'beneficiary');
                    $beneficiary->setPhoto($relativePath);
                }

                $entityManager->persist($beneficiary);
                $entityManager->flush();

                return $this->redirectToRoute('app_user_show', ['id' => $user->getId(), 'dominio' => $dominio]);
            }

            return $this->render('beneficiary/new.html.twig', [
                'form' => $form->createView(),
                'beneficiary' => $beneficiary,
                'user' => $user,
                'dominio' => $dominio,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }
    
    #[Route('/{id}', name: 'app_beneficiary_show', methods: ['GET'])]
    public function show(string $dominio, Beneficiary $beneficiary): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('beneficiary/show.html.twig', [
                'beneficiary' => $beneficiary,
                'dominio' => $dominio,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}/edit', name: 'app_beneficiary_edit', methods: ['GET', 'POST'])]
    public function edit(string $dominio, Request $request, Beneficiary $beneficiary, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $form = $this->createForm(BeneficiaryType::class, $beneficiary);
            $form->handleRequest($request);
            $user = $beneficiary->getUser();


            if ($form->isSubmitted() && $form->isValid()) {

                /** @var UploadedFile $imageFile */
                $imageFile = $form->get('photo')->getData();

                if ($imageFile) {
                    $relativePath = $this->imageUploadService->uploadImage($imageFile, 'beneficiary');
                    $beneficiary->setPhoto($relativePath);
                }

                $beneficiary->setUpdatedAt(new \DateTimeImmutable());

                if (!$beneficiary->getUser()) {
                    // Por ejemplo, si lo tienes como objeto actual
                    $beneficiary->setUser($someUser);
                }
             
                $entityManager->flush();

                return $this->redirectToRoute('app_user_show', ['id' => $user->getId(), 'dominio' => $dominio]);
            }

            return $this->render('beneficiary/edit.html.twig', [
                'beneficiary' => $beneficiary,
                'form' => $form,
                'user' => $user,
                'dominio' => $dominio,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}', name: 'app_beneficiary_delete', methods: ['POST'])]
    public function delete(string $dominio, Request $request, Beneficiary $beneficiary, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            if ($this->isCsrfTokenValid('delete'.$beneficiary->getId(), $request->request->get('_token'))) {
                $user = $beneficiary->getUser(); // Recupera al usuario asociado
                $beneficiary->setStatus(Status::INACTIVE);
                $entityManager->flush();
        
                return $this->redirectToRoute('app_user_show', ['id' => $user->getId(), 'dominio' => $dominio]);
            }
        
            // Si el token es inválido, redirige de todas formas
            return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }
}
