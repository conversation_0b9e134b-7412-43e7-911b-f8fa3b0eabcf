<?php

namespace App\Controller;

use App\Service\TenantManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/{dominio}/soporte')]
final class SupportController extends AbstractController
{
    public function __construct(
        private readonly TenantManager $tenantManager
    ) {}

    #[Route('', name: 'app_support_index', methods: ['GET'])]
    public function index(string $dominio): Response
    {
        try {
            // Configurar tenant para mantener consistencia con el sistema
            $this->tenantManager->setCurrentTenant($dominio);

            return $this->render('support/index.html.twig', [
                'dominio' => $dominio,
                'page_title' => 'Soporte y Ayuda'
            ]);
        } catch (\Exception $e) {
            // En caso de error, mostrar página básica de soporte
            return $this->render('support/index.html.twig', [
                'dominio' => $dominio,
                'page_title' => 'Soporte y Ayuda',
                'basic_mode' => true
            ]);
        }
    }

    #[Route('/contacto', name: 'app_support_contact', methods: ['GET', 'POST'])]
    public function contact(string $dominio, Request $request): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            if ($request->isMethod('POST')) {
                // Aquí se puede implementar el envío de emails en el futuro
                $this->addFlash('success', 'Tu mensaje ha sido enviado. Te contactaremos pronto.');
                return $this->redirectToRoute('app_support_contact', ['dominio' => $dominio]);
            }

            return $this->render('support/contact.html.twig', [
                'dominio' => $dominio,
                'page_title' => 'Contacto'
            ]);
        } catch (\Exception $e) {
            return $this->render('support/contact.html.twig', [
                'dominio' => $dominio,
                'page_title' => 'Contacto',
                'basic_mode' => true
            ]);
        }
    }
}
