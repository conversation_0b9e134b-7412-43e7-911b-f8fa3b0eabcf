<?php

namespace App\Repository;

use App\Entity\App\Notification;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Notification>
 */
class NotificationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Notification::class);
    }

    /**
     * Find active notifications associated with the given companies
     *
     * @param array $companies Array of Company entities
     * @return Notification[] Returns an array of Notification objects
     */
    public function findActiveByCompanies(array $companies): array
    {
        if (empty($companies)) {
            return [];
        }

        $companyIds = array_map(function($company) {
            return $company->getId();
        }, $companies);

        return $this->createQueryBuilder('n')
            ->andWhere('n.status = :status')
            ->setParameter('status', \App\Enum\Status::ACTIVE)
            ->join('n.companies', 'c')
            ->andWhere('c.id IN (:companyIds)')
            ->setParameter('companyIds', $companyIds)
            ->orderBy('n.created_at', 'DESC')
            ->getQuery()
            ->getResult()
        ;
    }

    //    /**
    //     * @return Notification[] Returns an array of Notification objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('n')
    //            ->andWhere('n.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('n.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Notification
    //    {
    //        return $this->createQueryBuilder('n')
    //            ->andWhere('n.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
