<?php

namespace App\Service;

use AllowDynamicProperties;
use App\Entity\Master\Tenant;
use App\Enum\Status;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpFoundation\RequestStack;
use Doctrine\Persistence\ManagerRegistry;

#[AllowDynamicProperties]
class TenantManager
{
    private $currentTenant = null;
    private $doctrine;
    private $requestStack;

    public function __construct(
        ManagerRegistry $doctrine,
        RequestStack $requestStack,
        private readonly EntityManagerInterface $entityManager,
    ) {
        $this->doctrine = $doctrine;
        $this->requestStack = $requestStack;
    }

    public function setCurrentTenant(string $tenant): void
    {
        if (!$this->isValidTenant($tenant)) {
            $allowedTenants = implode(', ', $this->getAllowedTenants());
            throw new NotFoundHttpException(sprintf('Tenant "%s" no es válido. Los tenants permitidos son: %s', $tenant, $allowedTenants));
        }

        // Si el tenant ha cambiado, limpiar el entity manager anterior
        if ($this->currentTenant !== null && $this->currentTenant !== $tenant) {
            try {
                $oldEm = $this->doctrine->getManager($this->currentTenant);
                if ($oldEm->isOpen()) {
                    $oldEm->clear();
                }
            } catch (\Exception $e) {
                // Ignorar errores al limpiar el entity manager anterior
            }
        }

        $this->currentTenant = $tenant;

        // Guardar el tenant en la sesión (solo si hay una sesión disponible)
        try {
            $session = $this->requestStack->getSession();
            $session->set('current_tenant', $tenant);
        } catch (\Exception $e) {
            // Ignorar errores de sesión en contexto de consola
        }
    }

    public function getCurrentTenant(): ?string
    {
        if ($this->currentTenant) {
            return $this->currentTenant;
        }

        // Intentar obtener el tenant de la sesión (solo si hay una sesión disponible)
        try {
            $session = $this->requestStack->getSession();
            $tenant = $session->get('current_tenant');

            if ($tenant && $this->isValidTenant($tenant)) {
                $this->currentTenant = $tenant;
                return $tenant;
            }
        } catch (\Exception $e) {
            // Ignorar errores de sesión en contexto de consola
        }

        // Si no hay tenant en la sesión, intentar obtenerlo de la ruta
        $request = $this->requestStack->getCurrentRequest();
        if (!$request) {
            // En contexto de consola, no hay request disponible
            return null;
        }

        $tenant = $request->attributes->get('dominio');
        if (!$this->isValidTenant($tenant)) {
            throw new \RuntimeException('Invalid tenant');
        }

        $this->currentTenant = $tenant;

        // Guardar en sesión solo si está disponible
        try {
            $session = $this->requestStack->getSession();
            $session->set('current_tenant', $tenant);
        } catch (\Exception $e) {
            // Ignorar errores de sesión en contexto de consola
        }

        return $tenant;
    }

    public function getEntityManager(): EntityManagerInterface
    {
        $tenant = $this->getCurrentTenant();
        if (!$tenant) {
            throw new NotFoundHttpException('No se ha establecido un tenant actual.');
        }

        $em = $this->doctrine->getManager($tenant);

        // Verificar si el EntityManager está cerrado y reabrirlo si es necesario
        if (!$em->isOpen()) {
            $this->doctrine->resetManager($tenant);
            $em = $this->doctrine->getManager($tenant);
        }

        return $em;
    }

    public function isValidTenant(string $tenant): bool
    {
        return array_key_exists($tenant, $this->getAllowedTenants());
    }

    public function getAllowedTenants(): array
    {
        $em = $this->doctrine->getManager('Master'); // <--- EVITAR RECURSIVIDAD

        $tenants = $em->getRepository(Tenant::class)->findBy(['status' => Status::ACTIVE]);

        return array_reduce($tenants, function ($carry, $tenant) {
            $carry[$tenant->getDominio()] = $tenant->getDatabaseName();
            return $carry;
        }, []);
    }


    private function getTenantDatabaseName(string $tenant): string
    {

        $databaseMapping = $this->getAllowedTenants();

        if (!isset($databaseMapping[$tenant])) {
            throw new NotFoundHttpException(sprintf('No hay base de datos configurada para el tenant "%s".', $tenant));
        }

        return $databaseMapping[$tenant];
    }

    public function getTenantConnection(): array
    {
        $tenant = $this->getCurrentTenant();
        if (!$tenant) {
            throw new NotFoundHttpException('No se ha establecido un tenant actual.');
        }

        $em = $this->getEntityManager();
        $connection = $em->getConnection();
        $params = $connection->getParams();

        return [
            'url' => sprintf(
                '%s://%s:%s@%s:%d/%s',
                $params['driver'],
                $params['user'],
                $params['password'],
                $params['host'],
                $params['port'] ?? 3306,
                $this->getTenantDatabaseName($tenant)
            )
        ];
    }

    /**
     * Limpia el EntityManager actual para evitar problemas de proxy
     */
    public function clearCurrentEntityManager(): void
    {
        try {
            $em = $this->getEntityManager();
            if ($em->isOpen()) {
                $em->clear();
            }
        } catch (\Exception $e) {
            // Ignorar errores al limpiar
        }
    }

    /**
     * Limpia todos los EntityManagers para evitar problemas de proxy entre tenants
     */
    public function clearAllEntityManagers(): void
    {
        $entityManagers = array_keys($this->getAllowedTenants());

        foreach ($entityManagers as $emName) {
            try {
                $em = $this->doctrine->getManager($emName);
                if ($em->isOpen()) {
                    $em->clear();
                }
            } catch (\Exception $e) {
                // Ignorar errores de entity managers no disponibles
                continue;
            }
        }
    }

}
