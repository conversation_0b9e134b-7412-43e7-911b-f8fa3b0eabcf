<?php

namespace App\Service;

use App\Entity\App\User;
use Doctrine\ORM\QueryBuilder;
use Symfony\Bundle\SecurityBundle\Security;

class RegionAccessService
{
    private $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    /**
     * Apply region-based filtering to a query builder based on the current user's role
     * 
     * @param QueryBuilder $queryBuilder The query builder to modify
     * @param string $entityAlias The alias used for the main entity in the query
     * @param string $regionJoinPath The path to join to the regions table (e.g., 'e.regions')
     * @return QueryBuilder The modified query builder
     */
    public function applyRegionFilter(QueryBuilder $queryBuilder, string $entityAlias, string $regionJoinPath): QueryBuilder
    {
        /** @var User $user */
        $user = $this->security->getUser();

        if (!$user) {
            return $queryBuilder;
        }

        $roles = $user->getRoles();

        // ROLE_ADMIN has access to all regions, no filtering needed
        if (in_array('ROLE_ADMIN', $roles)) {
            return $queryBuilder;
        }

        // ROLE_LIDER has access only to assigned regions
        if (in_array('ROLE_LIDER', $roles)) {
            $regions = $user->getRegions();

            if ($regions->isEmpty()) {
                // If no regions assigned, return no results
                return $queryBuilder->andWhere('1 = 0');
            }

            $regionIds = [];
            foreach ($regions as $region) {
                $regionIds[] = $region->getId();
            }

            // Join with regions if not already joined
            $joinParts = $queryBuilder->getDQLPart('join');
            $isJoined = false;

            if (!empty($joinParts)) {
                foreach ($joinParts as $joinPart) {
                    foreach ($joinPart as $join) {
                        if ($join->getJoin() === $regionJoinPath) {
                            $isJoined = true;
                            break 2;
                        }
                    }
                }
            }

            if (!$isJoined) {
                $queryBuilder->join($regionJoinPath, 'reg');
            }

            return $queryBuilder->andWhere('reg.id IN (:regionIds)')
                ->setParameter('regionIds', $regionIds);
        }

        return $queryBuilder;
    }
}
