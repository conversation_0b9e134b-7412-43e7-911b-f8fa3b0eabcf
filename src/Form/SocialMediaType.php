<?php

namespace App\Form;

use App\Entity\App\Company;
use App\Entity\App\SocialMedia;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;

class SocialMediaType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('company', EntityType::class, [
                'class' => Company::class,
                'choice_label' => 'name',
                'label' => 'Empresa',
                'required' => true,
            ])
            ->add('title', TextType::class, [
                'label' => 'Título',
                'required' => true,
            ])
            ->add('description', TextType::class, [
                'label' => 'Descripción',
                'required' => false,
            ])
            ->add('url', UrlType::class, [
                'label' => 'URL',
                'required' => true,
            ])
            ->add('platform', TextType::class, [
                'label' => 'Plataforma',
                'required' => true,
            ])
            ->add('image', FileType::class, [
                'label' => 'Imagen destacada',
                'required' => false,
                'mapped' => false,
                'constraints' => [
                    new File([
                        'maxSize' => '2048k',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Por favor sube una imagen válida (JPEG, PNG, WEBP)',
                    ])
                ],
            ])
            ->add('startDate', DateTimeType::class, [
                'label' => 'Fecha de inicio',
                'widget' => 'single_text',
                'required' => true,
            ])
            ->add('endDate', DateTimeType::class, [
                'label' => 'Fecha de fin',
                'widget' => 'single_text',
                'required' => true,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => SocialMedia::class,
        ]);
    }
}
