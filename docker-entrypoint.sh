#!/bin/bash

echo "🚀 Iniciando entrypoint de la aplicación..."
set -e

cd /var/www/html

# Composer install solo si falta vendor/autoload.php
if [ ! -f vendor/autoload.php ]; then
  echo "Ejecutando composer install..."
  composer install --no-interaction --optimize-autoloader || { echo "composer install falló"; exit 1; }
fi

# ⚠️ PROTECCIÓN CONTRA PÉRDIDA DE DATOS EN PRODUCCIÓN ⚠️
if [ "$APP_ENV" = "prod" ]; then
    echo "🔒 MODO PRODUCCIÓN DETECTADO - Protecciones de datos activadas"
    export DOCTRINE_DISABLE_SCHEMA_DROP=1
fi

# Esperar a que MySQL esté disponible
echo "Esperando a que MySQL (mysql:3306) esté disponible..."
until mysqladmin ping -h"mysql" -P3306 --silent; do
    sleep 2
    echo "Esperando a MySQL (mysql:3306)..."
done

echo "Verificación de conexión MySQL completada."
echo "=== CONFIGURANDO BASE DE DATOS ==="

# Crear base de datos TS
echo "🗄️ Creando base de datos TS..."
DATABASE_URL="$DATABASE_URL_TS" php bin/console doctrine:database:create --connection=ts --if-not-exists --env=prod || true

# Configurar esquema para TS
echo "🗄️ Configurando esquema para tenant TS..."
TS_TABLES_COUNT=$(DATABASE_URL="$DATABASE_URL_TS" php bin/console dbal:run-sql "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE()" --env=prod 2>/dev/null | grep -o '[0-9]\+' | tail -1 || echo "0")

if [ "$TS_TABLES_COUNT" -gt 0 ]; then
    echo "✅ Base de datos TS existente detectada con $TS_TABLES_COUNT tablas. Ejecutando migraciones..."
    DATABASE_URL="$DATABASE_URL_TS" php bin/console doctrine:migrations:migrate --em=ts --no-interaction --env=prod || {
        echo "⚠️ Error en migraciones TS. Verificando estado..."
        DATABASE_URL="$DATABASE_URL_TS" php bin/console doctrine:migrations:status --em=ts --env=prod || true
        echo "🔧 Intentando sincronizar estado de migraciones TS..."
        DATABASE_URL="$DATABASE_URL_TS" php bin/console doctrine:migrations:version --add --all --em=ts --no-interaction --env=prod || true
    }
else
    echo "🆕 Base de datos TS vacía detectada. Creando schema inicial..."
    DATABASE_URL="$DATABASE_URL_TS" php bin/console doctrine:schema:create --em=ts --env=prod || {
        echo "⚠️ Error creando schema TS. Intentando con migraciones..."
        DATABASE_URL="$DATABASE_URL_TS" php bin/console doctrine:migrations:migrate --em=ts --no-interaction --env=prod || true
    }
    DATABASE_URL="$DATABASE_URL_TS" php bin/console doctrine:migrations:version --add --all --em=ts --no-interaction --env=prod || true
fi

# Ejecutar migraciones para tenant SNT si la variable existe
if [ -n "$DATABASE_URL_SNT" ]; then
    echo "🗄️ Creando base de datos SNT..."
    DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:database:create --connection=SNT --if-not-exists --env=prod || true

    echo "🗄️ Configurando esquema para tenant SNT..."
    SNT_TABLES_COUNT=$(DATABASE_URL="$DATABASE_URL_SNT" php bin/console dbal:run-sql "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE()" --env=prod 2>/dev/null | grep -o '[0-9]\+' | tail -1 || echo "0")

    if [ "$SNT_TABLES_COUNT" -gt 0 ]; then
        echo "✅ Base de datos SNT existente detectada con $SNT_TABLES_COUNT tablas. Ejecutando migraciones..."
        DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:migrations:migrate --em=SNT --no-interaction --env=prod || {
            echo "⚠️ Error en migraciones SNT. Verificando estado..."
            DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:migrations:status --em=SNT --env=prod || true
            echo "🔧 Intentando sincronizar estado de migraciones SNT..."
            DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:migrations:version --add --all --em=SNT --no-interaction --env=prod || true
        }
    else
        echo "🆕 Base de datos SNT vacía detectada. Creando schema inicial..."
        DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:schema:create --em=SNT --env=prod || {
            echo "⚠️ Error creando schema SNT. Intentando con migraciones..."
            DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:migrations:migrate --em=SNT --no-interaction --env=prod || true
        }
        DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:migrations:version --add --all --em=SNT --no-interaction --env=prod || true
    fi
fi

# Ejecutar migraciones para tenant MASTER si la variable existe
if [ -n "$DATABASE_URL_MASTER" ]; then
    echo "🗄️ Creando base de datos MASTER..."
    DATABASE_URL="$DATABASE_URL_MASTER" php bin/console doctrine:database:create --connection=Master --if-not-exists --env=prod || true

    echo "🗄️ Configurando esquema para tenant MASTER..."
    MASTER_TABLES_COUNT=$(DATABASE_URL="$DATABASE_URL_MASTER" php bin/console dbal:run-sql "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE()" --env=prod 2>/dev/null | grep -o '[0-9]\+' | tail -1 || echo "0")

    if [ "$MASTER_TABLES_COUNT" -gt 0 ]; then
        echo "✅ Base de datos MASTER existente detectada con $MASTER_TABLES_COUNT tablas. Ejecutando migraciones..."
        DATABASE_URL="$DATABASE_URL_MASTER" php bin/console doctrine:migrations:migrate --em=Master --no-interaction --env=prod || {
            echo "⚠️ Error en migraciones MASTER. Verificando estado..."
            DATABASE_URL="$DATABASE_URL_MASTER" php bin/console doctrine:migrations:status --em=Master --env=prod || true
            echo "🔧 Intentando sincronizar estado de migraciones MASTER..."
            DATABASE_URL="$DATABASE_URL_MASTER" php bin/console doctrine:migrations:version --add --all --em=Master --no-interaction --env=prod || true
        }
    else
        echo "🆕 Base de datos MASTER vacía detectada. Creando schema inicial..."
        DATABASE_URL="$DATABASE_URL_MASTER" php bin/console doctrine:schema:create --em=Master --env=prod || {
            echo "⚠️ Error creando schema MASTER. Intentando con migraciones..."
            DATABASE_URL="$DATABASE_URL_MASTER" php bin/console doctrine:migrations:migrate --em=Master --no-interaction --env=prod || true
        }
        DATABASE_URL="$DATABASE_URL_MASTER" php bin/console doctrine:migrations:version --add --all --em=Master --no-interaction --env=prod || true
    fi
fi

# Limpiar caché Symfony
echo "🧹 Limpiando caché Symfony..."
php bin/console cache:clear --env=prod || { echo "cache:clear falló"; exit 1; }

# Crear directorio JWT si no existe
mkdir -p config/jwt

# Generar claves JWT si no existen
if [ ! -f config/jwt/private.pem ]; then
    php bin/console lexik:jwt:generate-keypair --skip-if-exists --no-interaction
fi

# Permisos
chown -R www-data:www-data var/ config/jwt/
chmod -R 777 var/
chmod 644 config/jwt/private.pem config/jwt/public.pem

# Iniciar Apache
exec apache2-foreground
