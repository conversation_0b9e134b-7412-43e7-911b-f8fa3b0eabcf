/* ========================================
   FORM TEMPLATES STYLES
   Sistema de Gestión de Formularios - SNTIASG
   ======================================== */

/* ========================================
   ESTADO VACÍO - Empty State
   ======================================== */

.empty-state-container {
    padding: 3rem 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.empty-state-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* ========================================
   TABLA MEJORADA - Enhanced Table
   ======================================== */

.table-row-hover {
    transition: all 0.2s ease;
}

.table-row-hover:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.table-footer {
    background: rgba(255, 255, 255, 0.05) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #fff !important;
}

.table-footer .text-muted {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* ========================================
   BOTONES DE ACCIÓN - Action Buttons
   ======================================== */

.btn-group .btn {
    margin: 0 2px;
    border-radius: 6px !important;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-outline-info {
    color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    background: rgba(23, 162, 184, 0.1);
}

.btn-outline-info:hover {
    background-color: #17a2b8 !important;
    color: #fff !important;
}

.btn-outline-warning {
    color: #ffc107 !important;
    border-color: #ffc107 !important;
    background: rgba(255, 193, 7, 0.1);
}

.btn-outline-warning:hover {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.btn-outline-danger {
    color: #dc3545 !important;
    border-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.1);
}

.btn-outline-danger:hover {
    background-color: #dc3545 !important;
    color: #fff !important;
}

/* ========================================
   BADGES Y ELEMENTOS INFORMATIVOS
   ======================================== */

.badge.bg-info {
    background-color: rgba(23, 162, 184, 0.2) !important;
    color: #17a2b8 !important;
    border: 1px solid rgba(23, 162, 184, 0.3);
    font-weight: 500;
}

/* ========================================
   HEADER MEJORADO - Enhanced Header
   ======================================== */

.header-sntiasg-b h1 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.header-sntiasg-b p {
    font-size: 1.1rem;
    opacity: 0.8;
}

/* ========================================
   BOTÓN PRINCIPAL CTA
   ======================================== */

.btn-success.btn-lg {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-transform: none;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-success.btn-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
}

.btn-success.btn-lg:active {
    transform: translateY(0);
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
    .empty-state-container {
        padding: 2rem 1rem;
    }
    
    .empty-state-icon i {
        font-size: 3rem !important;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .btn-group .btn {
        margin: 0;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .table-container {
        margin: 0 -15px;
        border-radius: 0;
    }
    
    .header-sntiasg-b h1 {
        font-size: 1.5rem;
    }
    
    .header-sntiasg-b p {
        font-size: 1rem;
    }
}

/* ========================================
   ANIMACIONES SUTILES
   ======================================== */

.table-container {
    animation: fadeInUp 0.5s ease-out;
}

.empty-state-container {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* ========================================
   MEJORAS DE ACCESIBILIDAD
   ======================================== */

.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    outline: none;
}

.table th {
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: none;
}

/* Mejorar contraste para texto en tabla */
.styled-table tbody td {
    color: rgba(255, 255, 255, 0.95) !important;
}

.styled-table thead th {
    color: rgba(255, 255, 255, 0.95) !important;
}

/* ========================================
   COMPONENTES REUTILIZABLES - Form System
   ======================================== */

/* Header Estandarizado para Formularios */
.form-header-enhanced {
    background: linear-gradient(135deg, #175E82 0%, #1a6b91 100%);
    border-bottom: 3px solid rgba(255, 255, 255, 0.1);
}

.form-header-enhanced h1 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #fff;
}

.form-header-enhanced .subtitle {
    font-size: 1rem;
    opacity: 0.85;
    color: #fff;
}

.form-header-enhanced .breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    margin: 0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-header-enhanced .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.form-header-enhanced .breadcrumb-item.active {
    color: #fff;
}

/* Cards Modernas para Formularios */
.form-card-modern {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.form-card-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.form-card-modern .card-header {
    background: linear-gradient(135deg, #175E82 0%, #1a6b91 100%);
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    padding: 1rem 1.5rem;
}

.form-card-modern .card-header h4,
.form-card-modern .card-header h5 {
    color: #fff;
    margin: 0;
    font-weight: 600;
}

.form-card-modern .card-body {
    padding: 1.5rem;
}

/* Grupos de Acciones Mejorados */
.form-actions-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

.form-actions-group .btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    border: none;
}

.form-actions-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.form-actions-group .btn-primary {
    background: linear-gradient(135deg, #175E82 0%, #1a6b91 100%);
}

.form-actions-group .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.form-actions-group .btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

.form-actions-group .btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

/* Campos de Formulario Mejorados */
.form-field-enhanced .form-control,
.form-field-enhanced .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-field-enhanced .form-control:focus,
.form-field-enhanced .form-select:focus {
    border-color: #175E82;
    box-shadow: 0 0 0 0.25rem rgba(23, 94, 130, 0.15);
    background: #fff;
}

.form-field-enhanced .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-field-enhanced .form-text {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Estados Vacíos Reutilizables */
.form-empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.form-empty-state .empty-icon {
    font-size: 3rem;
    opacity: 0.5;
    margin-bottom: 1rem;
    color: #175E82;
}

.form-empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: 600;
}

.form-empty-state p {
    color: #6c757d;
    margin-bottom: 1.5rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Tablas Responsivas Mejoradas */
.form-table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.form-table-responsive .table {
    margin-bottom: 0;
    background: transparent;
}

/* ========================================
   TABLA DE CAMPOS - Fields Table
   ======================================== */

.fields-table {
    font-size: 0.9rem;
}

.fields-table thead th {
    background: linear-gradient(135deg, #175E82 0%, #1a6b91 100%);
    color: #fff;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    padding: 0.75rem 0.5rem;
}

.fields-table tbody td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.fields-table .field-label {
    color: #2c3e50;
    font-size: 0.9rem;
}

/* Etiquetas de Tipo de Campo */
.field-type-tag {
    display: inline-block;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
    border: 1px solid rgba(21, 101, 192, 0.2);
}

/* Etiquetas de Campo Obligatorio */
.field-required-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.field-required-tag.required {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #2e7d32;
    border: 1px solid rgba(46, 125, 50, 0.2);
}

.field-required-tag.optional {
    background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
    color: #616161;
    border: 1px solid rgba(97, 97, 97, 0.2);
}

/* Botones de Acción Mejorados */
.field-actions {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
    align-items: center;
}

.btn-action {
    padding: 0.375rem 0.5rem;
    border-radius: 6px;
    border: 1px solid transparent;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    min-width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-edit {
    background: #fd7e14;
    color: #fff !important;
    border-color: #fd7e14;
}

.btn-edit:hover {
    background: #e8650e;
    color: #fff !important;
    border-color: #dc5f0d;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
}

.btn-delete {
    background: #dc3545;
    color: #fff !important;
    border-color: #dc3545;
}

.btn-delete:hover {
    background: #c82333;
    color: #fff !important;
    border-color: #bd2130;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Hover de Filas Mejorado */
.fields-table .table-row-hover:hover {
    background-color: rgba(23, 94, 130, 0.03) !important;
    transform: none;
    box-shadow: none;
}

/* ========================================
   TABLA DE CAMPOS SHOW - Estilo Sutil
   ======================================== */

.fields-table-show {
    font-size: 0.9rem;
}

.fields-table-show thead th {
    background: #ffffff;
    color: #6c757d;
    font-weight: 500;
    font-size: 0.85rem;
    text-transform: none;
    letter-spacing: 0.2px;
    border: none;
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #dee2e6;
}

.fields-table-show tbody td {
    padding: 0.6rem 0.5rem;
    vertical-align: middle;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.fields-table-show .field-label {
    color: #2c3e50;
    font-size: 0.85rem;
}

/* Hover más sutil para tabla show */
.fields-table-show .table-row-hover:hover {
    background-color: rgba(0, 123, 255, 0.02) !important;
    transform: none;
    box-shadow: none;
}

/* Etiquetas más discretas para tabla show */
.fields-table-show .field-type-tag {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 400;
}

.fields-table-show .field-required-tag {
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 400;
}

.fields-table-show .field-required-tag.required {
    background: #f8f9fa;
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.fields-table-show .field-required-tag.optional {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

/* Botones más discretos para tabla show */
.fields-table-show .btn-action {
    padding: 0.3rem 0.4rem;
    border-radius: 4px;
    border: 1px solid transparent;
    font-size: 0.7rem;
    transition: all 0.15s ease;
    min-width: 28px;
    height: 28px;
    background: transparent;
}

.fields-table-show .btn-action:hover {
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.fields-table-show .btn-edit {
    background: #fd7e14;
    color: #fff !important;
    border-color: #fd7e14;
}

.fields-table-show .btn-edit:hover {
    background: #e8650e;
    color: #fff !important;
    border-color: #dc5f0d;
    box-shadow: 0 1px 3px rgba(253, 126, 20, 0.3);
}

.fields-table-show .btn-delete {
    background: #dc3545;
    color: #fff !important;
    border-color: #dc3545;
}

.fields-table-show .btn-delete:hover {
    background: #c82333;
    color: #fff !important;
    border-color: #bd2130;
    box-shadow: 0 1px 3px rgba(220, 53, 69, 0.3);
}

/* Responsive para Tabla de Campos */
@media (max-width: 768px) {
    .field-actions {
        flex-direction: column;
        gap: 0.125rem;
    }

    .btn-action {
        font-size: 0.7rem;
        padding: 0.25rem 0.375rem;
        min-width: 28px;
        height: 28px;
    }

    .field-type-tag,
    .field-required-tag {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }

    .fields-table,
    .fields-table-show {
        font-size: 0.8rem;
    }
}

.form-table-responsive .table.fields-table thead th {
    background: linear-gradient(135deg, #175E82 0%, #1a6b91 100%);
    color: #fff;
    border: none;
    padding: 1rem;
    font-weight: 600;
    text-transform: none;
    letter-spacing: 0.5px;
}

.form-table-responsive .table.fields-table-show thead th {
    background: #ffffff !important;
    color: #6c757d !important;
    font-weight: 500 !important;
    font-size: 0.85rem !important;
    text-transform: none !important;
    letter-spacing: 0.2px !important;
    border: none !important;
    padding: 1rem 0.75rem !important;
    border-bottom: 1px solid #dee2e6 !important;
}

/* ========================================
   ESTILOS PARA PREVIEW - Preview Styles
   ======================================== */

.field-type-tag-preview {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
    padding: 0.15rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 400;
    text-transform: capitalize;
}

.field-required-tag-preview {
    display: inline-flex;
    align-items: center;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 400;
}

.field-required-tag-preview.required {
    background: #f8f9fa;
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.field-required-tag-preview.optional {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

/* ========================================
   BOTONES ESTANDARIZADOS - Standardized Buttons
   ======================================== */

/* Botón Regresar/Volver - Azul */
.btn-back {
    background: #0d6efd;
    color: #fff !important;
    border: 1px solid #0d6efd;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-back:hover {
    background: #0b5ed7;
    border-color: #0a58ca;
    color: #fff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
}

/* Botón Cancelar - Rojo */
.btn-cancel {
    background: #dc3545;
    color: #fff !important;
    border: 1px solid #dc3545;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-cancel:hover {
    background: #c82333;
    border-color: #bd2130;
    color: #fff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Botón Editar - Amarillo/Naranja */
.btn-edit-std {
    background: #fd7e14;
    color: #fff !important;
    border: 1px solid #fd7e14;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-edit-std:hover {
    background: #e8650e;
    border-color: #dc5f0d;
    color: #fff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
}

/* Botón Ver - Info */
.btn-view {
    background: #17a2b8;
    color: #fff !important;
    border: 1px solid #17a2b8;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-view:hover {
    background: #138496;
    border-color: #117a8b;
    color: #fff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

/* Botón Secundario - Gris */
.btn-secondary-std {
    background: #6c757d;
    color: #fff !important;
    border: 1px solid #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-secondary-std:hover {
    background: #5a6268;
    border-color: #545b62;
    color: #fff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

/* Versiones outline para fondos claros */
.btn-back-outline {
    background: transparent;
    color: #0d6efd !important;
    border: 1px solid #0d6efd;
}

.btn-back-outline:hover {
    background: #0d6efd;
    color: #fff !important;
}

.btn-cancel-outline {
    background: transparent;
    color: #dc3545 !important;
    border: 1px solid #dc3545;
}

.btn-cancel-outline:hover {
    background: #dc3545;
    color: #fff !important;
}

.btn-edit-outline {
    background: transparent;
    color: #fd7e14 !important;
    border: 1px solid #fd7e14;
}

.btn-edit-outline:hover {
    background: #fd7e14;
    color: #fff !important;
}

.form-table-responsive .table tbody td {
    padding: 0.75rem 1rem;
    border-color: rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.form-table-responsive .table tbody tr:hover {
    background-color: rgba(23, 94, 130, 0.05);
}

/* Navegación y Breadcrumbs */
.form-navigation {
    margin-bottom: 1rem;
}

.form-navigation .breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    margin: 0;
}

.form-navigation .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.form-navigation .breadcrumb-item.active {
    color: #fff;
}
