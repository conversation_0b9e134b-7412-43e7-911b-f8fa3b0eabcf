{% set dominio = app.request.attributes.get('dominio') %}

<nav class="navbar bg-sntiasg fixed-top">
  <div class="container-fluid my-3 d-flex justify-content-between align-items-center">
    <a class="ms-3" href="{{ path('app_dashboard', {'dominio': dominio}) }}">
      <img src="{{ asset('images/logos/logoTS.svg') }}" alt="Logo" class="logo-image" style="width: 85px; height: 89px">
    </a>

    <div class="d-flex align-items-center ms-auto me-3 gap-4">
      <div id="hoverButton" class="d-flex flex-row justify-content-around align-items-center" style="cursor: pointer;">
        <img src="{{ asset('images/home.svg') }}" alt="Inicio" class="logo-image-movil" id="homeIcon">
        <a href="{{ path('app_logout', {'dominio': dominio}) }}" id="btn-exit" class="btn d-flex align-items-center">
          <span class="me-2  align-items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="67" height="67" fill="currentColor" viewBox="0 0 24 24">
              <path d="M10 17l1.41-1.41L8.83 13H20v-2H8.83l2.58-2.59L10 7l-5 5 5 5z"/>
              <path d="M19 3H5c-1.1 0-2 .9-2 2v4h2V5h14v14H5v-4H3v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
            </svg>
          </span>
        </a>
      </div>
    </div>
  </div>

  {% set route = app.request.attributes.get('_route') %}
  <div class="info-box{% if route == 'app_dashboard' %} show{% endif %}" id="infoBox">

  <a href="{{ path('app_user_index', {'dominio': dominio}) }}" class="nav-link-item">AGREMIADOS</a>
    <span class="divider">|</span>
    <a href="{{ path('app_user_admin_index', {'dominio': dominio}) }}" class="nav-link-item">USUARIOS</a>
    <span class="divider">|</span>
    <a href="{{ path('app_company_index', {'dominio': dominio}) }}" class="nav-link-item">EMPRESAS</a>
    <span class="divider">|</span>
    <a href="{{ path('app_region_index', {'dominio': dominio}) }}" class="nav-link-item">REGIONES</a>
    <span class="divider">|</span>
    <a href="{{ path('app_benefit_index', {'dominio': dominio}) }}" class="nav-link-item">BENEFICIOS</a>
    <span class="divider">|</span>
    <a href="{{ path('app_notification_index', {'dominio': dominio}) }}" class="nav-link-item">NOTIFICACIONES</a>
    <span class="divider">|</span>
    <a href="{{ path('app_event_index', {'dominio': dominio}) }}" class="nav-link-item">EVENTOS</a>
    <span class="divider">|</span>
    <a href="{{ path('app_forms_index', {'dominio': dominio}) }}" class="nav-link-item">FORMULARIOS</a>
  </div>
</nav>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const infoBox = document.getElementById('infoBox');
    const homeIcon = document.getElementById('homeIcon');

    homeIcon.addEventListener('click', function () {
      infoBox.classList.toggle('show');
    });

    // Scroll behavior
    const navbar = document.querySelector('.navbar');
    window.addEventListener('scroll', function () {
      navbar.classList.toggle('scrolled', window.scrollY > 50);
    });
  });
</script>
