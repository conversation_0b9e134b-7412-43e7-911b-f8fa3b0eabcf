{% extends 'base.html.twig' %}
{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Form Templates{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('form-templates') }}
{% endblock %}

{% block body %}

    <section class="header-sntiasg-b py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h2 mb-2 font-weight-bold text-white">
                        <i class="fas fa-file-alt mr-3"></i> Plantillas de Formularios
                    </h1>
                    <p class="mb-0 text-white-50">Gestiona y organiza tus formularios personalizados</p>
                </div>
                <div class="col-md-4 text-md-end text-center mt-3 mt-md-0">
                    <a href="{{ path('app_forms_new', {'dominio': dominio}) }}" class="btn btn-success btn-lg shadow-sm px-4 py-2">
                        <i class="fas fa-plus-circle mr-2"></i> Crear Formulario
                    </a>
                </div>
            </div>
        </div>
    </section>

    <div class="container mt-4">

        <div class="px-4">
            {% if form_templates is empty %}
                <div class="text-center py-5">
                    <div class="empty-state-container">
                        <div class="empty-state-icon mb-4">
                            <i class="fas fa-file-alt" style="font-size: 4rem; color: #6c757d; opacity: 0.5;"></i>
                        </div>
                        <h3 class="text-white mb-3">¡Comienza creando tu primer formulario!</h3>
                        <p class="text-white-50 mb-4 mx-auto" style="max-width: 400px;">
                            Los formularios te permiten recopilar información de manera estructurada y eficiente.
                            Crea campos personalizados según tus necesidades.
                        </p>
                        <a href="{{ path('app_forms_new', {'dominio': dominio}) }}" class="btn btn-success btn-lg px-5 py-3">
                            <i class="fas fa-plus-circle mr-2"></i> Crear Mi Primer Formulario
                        </a>
                    </div>
                </div>
            {% else %}
                <div class="table-container table-responsive shadow-sm" style="border-radius: 12px; overflow: hidden;">
                <form id="exportForm" action="{{ path('app_forms_export_multiple', {'dominio': dominio}) }}" method="POST">
                    <table class="styled-table table-striped table-bordered text-center mb-0">
                        <thead class="table-primary text-dark">
                        <tr>
                            <th class="py-3">Nombre</th>
                            <th class="py-3">Descripción</th>
                            <th class="py-3">Campos</th>
                            <th class="py-3">Respuestas</th>
                            <th class="py-3">Fecha de Creación</th>
                            <th class="py-3">Acciones</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for form_template in form_templates %}
                            <tr class="table-row-hover">
                                <td class="py-3 fw-bold">{{ form_template.name }}</td>
                                <td class="py-3">
                                    <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ form_template.description }}">
                                        {{ form_template.description|slice(0, 50) }}{% if form_template.description|length > 50 %}...{% endif %}
                                    </span>
                                </td>
                                <td class="py-3">
                                    <span class="badge bg-info text-dark px-3 py-2">{{ form_template.fields_count }} campos</span>
                                </td>
                                <td class="py-3">
                                    <span class="badge bg-success text-white px-3 py-2">{{ form_template.responses_count ?? 0 }} respuestas</span>
                                </td>
                                <td class="py-3">{{ form_template.created_at|date('d/m/Y') }}</td>
                                <td class="py-3">
                                    <div class="btn-group" role="group">
                                        <a href="{{ path('app_forms_show', {'id': form_template.id, 'dominio': dominio}) }}"
                                           class="btn btn-outline-info btn-sm" title="Ver formulario">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ path('app_forms_submissions', {'id': form_template.id, 'dominio': dominio}) }}"
                                           class="btn btn-outline-success btn-sm" title="Ver respuestas">
                                            <i class="fas fa-inbox"></i>
                                        </a>
                                        <a href="{{ path('app_forms_edit', {'id': form_template.id, 'dominio': dominio}) }}"
                                           class="btn btn-outline-warning btn-sm" title="Editar formulario">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                data-bs-toggle="modal"
                                                data-bs-target="#deleteModal{{ form_template.id }}"
                                                title="Eliminar formulario">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            {% else %}
                                <tr>
                                    <td colspan="5" class="py-4 text-muted">
                                        <i class="fas fa-search mr-2"></i>
                                        No se encontraron formularios
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                        </form>

                        <div class="table-footer bg-light p-3 border-top">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        <span id="results-count">Mostrando {{ form_templates|length }} formulario{{ form_templates|length != 1 ? 's' : '' }}</span>
                                    </small>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <small class="text-muted">
                                        Última actualización: {{ "now"|date('d/m/Y H:i') }}
                                    </small>
                                </div>
                            </div>
                            <div id="no-results" class="text-center text-muted py-2" style="display: none;">
                                <i class="fas fa-search mr-2"></i>
                                No se encontraron formularios que coincidan con tu búsqueda.
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modales de Eliminación (fuera de la tabla) -->
    {% if form_templates is not empty %}
        {% for form_template in form_templates %}
            <div class="modal fade" id="deleteModal{{ form_template.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ form_template.id }}" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-sm">
                    <div class="modal-content border-0 shadow-lg" style="border-radius: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div class="modal-body text-center p-4">
                            <!-- Icono de advertencia -->
                            <div class="mb-3">
                                <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            </div>

                            <!-- Título más elegante -->
                            <h5 class="text-white fw-bold mb-3" style="font-size: 1.3rem;">
                                Eliminar Formulario
                            </h5>

                            <!-- Texto más conciso -->
                            <p class="text-white-50 mb-2" style="font-size: 0.95rem;">
                                ¿Está seguro de eliminar este formulario?
                            </p>
                            <p class="text-white fw-semibold mb-4" style="font-size: 1rem;">
                                "{{ form_template.name }}"
                            </p>

                            <!-- Botones más modernos -->
                            <div class="d-flex gap-2 justify-content-center">
                                <button type="button" class="btn btn-light btn-sm px-4" data-bs-dismiss="modal" style="border-radius: 25px;">
                                    <i class="fas fa-times me-1"></i> Cancelar
                                </button>
                                <form action="{{ path('app_forms_delete', {'id': form_template.id, 'dominio': dominio}) }}" method="post" style="display: inline;">
                                    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ form_template.id) }}">
                                    <button type="submit" class="btn btn-danger btn-sm px-4" style="border-radius: 25px;">
                                        <i class="fas fa-trash me-1"></i> Eliminar
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% endif %}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function () {

            const input = document.getElementById('filterTitle');
            const rows = document.querySelectorAll('tbody tr');
            const resultText = document.getElementById('results-count');
            const noResults = document.getElementById('no-results');

            // Función para filtrar formularios
            function filterForms() {
                const searchTerm = input ? input.value.toLowerCase().trim() : '';
                let count = 0;

                rows.forEach(row => {
                    const visible = Array.from(row.cells).some(cell =>
                        cell.textContent.toLowerCase().includes(searchTerm)
                    );
                    row.style.display = visible ? '' : 'none';
                    if (visible) count++;
                });

                if (resultText) {
                    resultText.textContent = `Mostrando ${count} formulario${count === 1 ? '' : 's'}`;
                }
                if (noResults) {
                    noResults.style.display = count === 0 ? 'block' : 'none';
                }
            }

            // Agregar filtro de búsqueda
            if (input) {
                input.addEventListener('input', filterForms);
            }
        });
    </script>
{% endblock %}
