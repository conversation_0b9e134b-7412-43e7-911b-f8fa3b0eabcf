{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Editar Formulario: {{ form_template.name }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('form-templates') }}
    <style>
        /* Estilos personalizados para Select2 de empresas */
        .select2-container--bootstrap-5 .select2-selection--multiple {
            min-height: 45px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 5px;
            transition: all 0.3s ease;
        }

        .select2-container--bootstrap-5.select2-container--focus .select2-selection--multiple {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
            background-color: #0d6efd;
            border: 1px solid #0d6efd;
            color: white;
            border-radius: 6px;
            padding: 4px 8px;
            margin: 2px;
            font-size: 0.875rem;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove {
            color: white;
            margin-right: 5px;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #ffcccc;
        }

        .select2-dropdown {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .companies-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            padding: 12px;
            margin-top: 8px;
        }
    </style>
{% endblock %}

{% block body %}
    <section class="header-sntiasg-b form-header-enhanced py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <nav class="form-navigation mb-3">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ path('app_forms_index', {'dominio': dominio}) }}">
                                    <i class="fas fa-file-alt mr-1"></i> Formularios
                                </a>
                            </li>
                            <li class="breadcrumb-item active">Editar: {{ form_template.name }}</li>
                        </ol>
                    </nav>
                    <h1><i class="fas fa-edit mr-3"></i> Editar Formulario</h1>
                    <p class="subtitle">Modifica la información y configuración del formulario "{{ form_template.name }}"</p>
                </div>
                <div class="col-md-4 text-md-end text-center mt-3 mt-md-0">
                    <div class="form-actions-group justify-content-end">
                        <a href="{{ path('app_forms_show', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-view">
                            <i class="fas fa-eye mr-2"></i> Ver
                        </a>
                        <a href="{{ path('app_forms_index', {'dominio': dominio}) }}" class="btn btn-back">
                            <i class="fas fa-arrow-left mr-2"></i> Regresar
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-card-modern">
                    <div class="card-header">
                        <h4><i class="fas fa-edit mr-2"></i> Información del Formulario</h4>
                    </div>
                    <div class="card-body form-field-enhanced">
                <!-- Flash messages are handled by SweetAlert2 in base template -->

                {% if errors is defined and errors %}
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            SwalHelper.validationErrors({{ errors|json_encode|raw }});
                        });
                    </script>
                {% endif %}

                <form method="post">
                    <div class="mb-3">
                        <label for="name" class="form-label">Nombre del Formulario</label>
                        <input type="text" id="name" name="name" class="form-control {% if errors.name is defined %}is-invalid{% endif %}"
                               value="{{ app.request.request.get('name') ?: form_template.name }}" required>
                        <small class="form-text text-muted">Nombre visible para los usuarios.</small>
                        {% if errors.name is defined %}
                            <div class="invalid-feedback">{{ errors.name }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Descripción</label>
                        <textarea id="description" name="description" class="form-control {% if errors.description is defined %}is-invalid{% endif %}" rows="4">{{ app.request.request.get('description') ?: form_template.description }}</textarea>
                        <small class="form-text text-muted">Describe el propósito del formulario.</small>
                        {% if errors.description is defined %}
                            <div class="invalid-feedback">{{ errors.description }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="companyIds" class="form-label fw-bold">
                            <i class="fas fa-building me-2 text-primary"></i>Empresas Autorizadas
                        </label>
                        <select id="companyIds" name="companyIds[]" class="form-select select2-multiple {% if errors.companyIds is defined %}is-invalid{% endif %}" multiple="multiple" data-placeholder="🌐 Seleccionar empresas (vacío = todas las empresas)">
                            {% for company in companies %}
                                <option value="{{ company.id }}" {% if company in form_template.companies %}selected{% endif %}>
                                    🏢 {{ company.name }}
                                </option>
                            {% endfor %}
                        </select>
                        <div class="companies-info">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    <small class="fw-bold text-dark">Configuración de Acceso:</small>
                                </div>
                                <div class="d-flex gap-3">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-globe text-info me-1"></i>
                                        <small class="text-dark"><strong>Vacío:</strong> Todas</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-lock text-warning me-1"></i>
                                        <small class="text-dark"><strong>Selección:</strong> Específicas</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% if errors.companyIds is defined %}
                            <div class="invalid-feedback">{{ errors.companyIds }}</div>
                        {% endif %}
                    </div>

                    <div class="form-actions-group justify-content-end mt-4">
                        <a href="{{ path('app_forms_show', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-cancel">
                            <i class="fas fa-times mr-2"></i> Cancelar
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save mr-2"></i> Guardar Cambios
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="form-card-modern mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4><i class="fas fa-list-alt mr-2"></i> Campos del Formulario</h4>
                <a href="{{ path('app_forms_fields_new', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-success btn-sm">
                    <i class="fas fa-plus mr-2"></i> Agregar Campo
                </a>
            </div>

            <div class="card-body p-0">
                {% set activeFields = form_template.formTemplateFields|filter(f => f.status.value == '1') %}
                {% if activeFields|length > 0 %}
                    <div class="form-table-responsive">
                        <table id="formsTableEdit" class="table fields-table-show text-center align-middle mb-0">
                            <thead>
                            <tr>
                                <th class="py-2">Etiqueta</th>
                                <th class="py-2">Tipo</th>
                                <th class="py-2">Obligatorio</th>
                                <th class="py-2">Acciones</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for field in activeFields %}
                                <tr class="table-row-hover">
                                    <td class="py-2 fw-bold field-label">{{ field.label }}</td>
                                    <td class="py-2">
                                        <span class="field-type-tag">{{ field.type|capitalize }}</span>
                                    </td>
                                    <td class="py-2">
                                        {% if field.isRequired %}
                                            <span class="field-required-tag required">
                                                <i class="fas fa-check-circle me-1"></i>Sí
                                            </span>
                                        {% else %}
                                            <span class="field-required-tag optional">
                                                <i class="fas fa-minus-circle me-1"></i>No
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="py-2">
                                        <div class="field-actions">
                                            <a href="{{ path('app_forms_fields_edit', {'id': form_template.id, 'fieldId': field.id, 'dominio': dominio}) }}"
                                               class="btn btn-action btn-edit"
                                               title="Editar campo">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-action btn-delete"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#deleteFieldModal{{ field.id }}"
                                                    title="Eliminar campo">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal de Eliminación -->
                                        <div class="modal fade" id="deleteFieldModal{{ field.id }}" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog modal-dialog-centered">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger text-white">
                                                        <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i> Confirmar Eliminación</h5>
                                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        ¿Estás seguro de eliminar el campo "<strong>{{ field.label }}</strong>"?
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                                        <form action="{{ path('app_forms_fields_delete', {'id': form_template.id, 'fieldId': field.id, 'dominio': dominio}) }}" method="post">
                                                            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ field.id) }}">
                                                            <button type="submit" class="btn btn-danger">Eliminar</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="form-empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-plus-square"></i>
                        </div>
                        <h3>¡Agrega tu primer campo!</h3>
                        <p>Los campos definen qué información recopilarás en este formulario. Puedes agregar texto, fechas, archivos y más.</p>
                        <a href="{{ path('app_forms_fields_new', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-success">
                            <i class="fas fa-plus mr-2"></i> Agregar Primer Campo
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar Select2 para el selector de empresas con configuración mejorada
            $('#companyIds').select2({
                theme: 'bootstrap-5',
                placeholder: '🌐 Seleccionar empresas (vacío = todas las empresas)',
                allowClear: true,
                width: '100%',
                closeOnSelect: false,
                tags: false,
                tokenSeparators: [','],
                escapeMarkup: function (markup) {
                    return markup;
                },
                templateResult: function(option) {
                    if (!option.id) return option.text;
                    return $('<span><i class="fas fa-building me-2"></i>' + option.text + '</span>');
                },
                templateSelection: function(option) {
                    if (!option.id) return option.text;
                    return $('<span><i class="fas fa-building me-1"></i>' + option.text + '</span>');
                }
            });

            // Agregar evento para mostrar información cuando se selecciona/deselecciona
            $('#companyIds').on('select2:select select2:unselect', function (e) {
                const selectedCount = $(this).val() ? $(this).val().length : 0;
                const totalCompanies = $(this).find('option').length;

                if (selectedCount === 0) {
                    console.log('✅ Formulario disponible para TODAS las empresas');
                } else {
                    console.log(`🔒 Formulario restringido a ${selectedCount} de ${totalCompanies} empresas`);
                }
            });

            $('#formsTableEdit').DataTable({
                responsive: true,
                language: {
                    "sProcessing": "Procesando...",
                    "sLengthMenu": "Mostrar _MENU_ registros",
                    "sZeroRecords": "No se encontraron resultados",
                    "sEmptyTable": "Ningún dato disponible",
                    "sInfo": "Mostrando _START_ a _END_ de _TOTAL_ registros",
                    "sInfoEmpty": "Mostrando 0 a 0 de 0 registros",
                    "sInfoFiltered": "(filtrado de _MAX_ registros en total)",
                    "sSearch": "Buscar:",
                    "oPaginate": {
                        "sFirst": "Primero",
                        "sLast": "Último",
                        "sNext": "Siguiente",
                        "sPrevious": "Anterior"
                    }
                },
                order: [[0, 'asc']],
                pageLength: 10,
                lengthMenu: [[5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"]]
            });
        });
    </script>
{% endblock %}
