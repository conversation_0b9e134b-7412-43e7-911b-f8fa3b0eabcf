{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Envíos del Formulario: {{ form_template.name }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('form-templates') }}
{% endblock %}

{% block body %}
    <section class="header-sntiasg-b form-header-enhanced py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <nav class="form-navigation mb-3">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ path('app_forms_index', {'dominio': dominio}) }}">
                                    <i class="fas fa-file-alt mr-1"></i> Formularios
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ path('app_forms_show', {'id': form_template.id, 'dominio': dominio}) }}">
                                    {{ form_template.name }}
                                </a>
                            </li>
                            <li class="breadcrumb-item active">Envíos</li>
                        </ol>
                    </nav>
                    <h1><i class="fas fa-inbox mr-3"></i> Envíos del Formulario</h1>
                    <p class="subtitle">Revisa y gestiona todas las respuestas recibidas para "{{ form_template.name }}"</p>
                </div>
                <div class="col-md-4 text-md-end text-center mt-3 mt-md-0">
                    <div class="form-actions-group justify-content-end">
                        <a href="{{ path('app_forms_show', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left mr-2"></i> Regresar
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container mt-4">
        <div class="form-card-modern">
            <div class="card-header">
                <h4><i class="fas fa-list mr-2"></i> Registros Recibidos</h4>
            </div>
            <div class="card-body">
                {% for message in app.flashes('success') %}
                    <div class="alert alert-success">
                        {{ message }}
                    </div>
                {% endfor %}

                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Usuario</th>
                            <th>Enviado</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for submission in submissions %}
                            <tr>
                                <td>{{ submission.id }}</td>
                                <td>
                                    {% if submission.user %}
                                        {{ submission.user.email }}
                                    {% else %}
                                        Anónimo
                                    {% endif %}
                                </td>
                                <td>{{ submission.createdAt|date('d/m/Y H:i') }}</td>
                                <td>
                                    {% if submission.status.value == 'A' %}
                                        <span class="badge bg-success">Activo</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactivo</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ path('app_forms_submission_show', {'id': submission.id, 'dominio': dominio}) }}" class="btn btn-info btn-sm fw-bold">
                                        <i class="fas fa-eye"></i> Ver
                                    </a>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center">No se encontraron registros</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}