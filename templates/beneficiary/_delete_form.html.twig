
<button type="button" class="btn-red" data-bs-toggle="modal" data-bs-target="#modalDeleteBeneficiary" data-form-id="delete-form-{{ beneficiary.id }}">ELIMINAR</button>

<form id="delete-form-{{ beneficiary.id }}" method="post" action="{{ path('app_beneficiary_delete', {'id': beneficiary.id}) }}">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ beneficiary.id) }}">
</form>

<div class="modal fade" id="modalDeleteBeneficiary" tabindex="-1" aria-labelledby="modalDeleteBeneficiaryLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content border-0 shadow-lg" style="border-radius: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="modal-body text-center p-4">
                <!-- Icono de beneficiario -->
                <div class="mb-3">
                    <i class="fas fa-user-minus text-warning" style="font-size: 3rem;"></i>
                </div>

                <!-- Título elegante -->
                <h5 class="text-white fw-bold mb-3" style="font-size: 1.3rem;" id="modalDeleteBeneficiaryLabel">
                    Eliminar Beneficiario
                </h5>

                <!-- Texto conciso -->
                <p class="text-white-50 mb-4" style="font-size: 0.95rem;">
                    ¿Está seguro de eliminar este beneficiario?
                </p>

                <!-- Botones modernos -->
                <div class="d-flex gap-2 justify-content-center">
                    <button type="button" class="btn btn-light btn-sm px-4" data-bs-dismiss="modal" style="border-radius: 25px;">
                        <i class="fas fa-times me-1"></i> Cancelar
                    </button>
                    <button id="btnConfirmDelete" type="button" class="btn btn-danger btn-sm px-4" style="border-radius: 25px;">
                        <i class="fas fa-user-minus me-1"></i> Eliminar
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        let formToSubmit = null;

        document.querySelectorAll('.btn-red').forEach(button => {
            button.addEventListener('click', function () {
                const formId = button.getAttribute('data-form-id');
                formToSubmit = document.getElementById(formId);
            });
        });

        const confirmBtn = document.getElementById('btnConfirmDelete');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', function () {
                if (formToSubmit) {
                    formToSubmit.submit();
                    formToSubmit = null;
                }
            });
        }
    });
</script>
