<!DOCTYPE html>
<html>
{% set dominio = app.request.attributes.get('dominio') %}

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}{% endblock %}</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=League+Spartan&family=Manjari&family=Montserrat&display=swap" rel="stylesheet">

    {% block stylesheets %}
        {{ encore_entry_link_tags('app') }}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endblock %}

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous"/>
    <!-- TEST: FontAwesome 5 fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" crossorigin="anonymous"/>
    <!-- TEST: FontAwesome 4 fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" crossorigin="anonymous"/>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- FOSJsRoutingBundle -->
    <script>
        // Inicializar objeto fos si no existe
        window.fos = window.fos || {};
        window.fos.Router = window.fos.Router || {};
        window.fos.Router.setData = window.fos.Router.setData || function(data) {
            console.log('FOSJsRouting data loaded:', data);
        };
    </script>
    <script src="{{ asset('bundles/fosjsrouting/js/router.min.js') }}" onerror="console.error('Error loading FOSJsRouting router.min.js')"></script>
    <script src="{{ path('fos_js_routing_js', { callback: 'fos.Router.setData' }) }}" onerror="console.error('Error loading FOSJsRouting data')"></script>

    {% block javascripts %}
        {{ encore_entry_script_tags('app') }}
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

        <!-- SweetAlert2 -->
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <!-- SweetAlert2 Helper -->
        <script src="{{ asset('js/sweetalert-helper.js') }}"></script>
    {% endblock %}
</head>
<body>

{% block navbar %}
    <nav class="navbar navbar-expand-lg fixed-top"style="background-color: #0B3F61;">
        <div class="container">
            <a class="navbar-brand" href="{{ path('app_default', {'dominio': dominio}) }}">
                <img src="{{ asset('images/logos/logoTS.svg') }}" alt="Logo" style="width: 60px; height: auto">
            </a>
        </div>
    </nav>
{% endblock %}

<!-- Flash messages will be handled by SweetAlert2 -->
{% if app.flashes|length > 0 %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            {% for label, messages in app.flashes %}
                {% for message in messages %}
                    {% set alertType = label == 'error' ? 'error' : (label == 'success' ? 'success' : (label == 'warning' ? 'warning' : 'info')) %}
                    Swal.fire({
                        icon: '{{ alertType }}',
                        title: '{{ alertType|title }}',
                        text: '{{ message|e('js') }}',
                        timer: {{ alertType == 'success' ? 3000 : 5000 }},
                        timerProgressBar: true,
                        showConfirmButton: {{ alertType == 'error' ? 'true' : 'false' }},
                        toast: true,
                        position: 'top-end',
                        showCloseButton: true
                    });
                {% endfor %}
            {% endfor %}
        });
    </script>
{% endif %}

{% block body %}{% endblock %}

<!-- Footer for public pages -->
{% block footer %}
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">© {{ "now"|date("Y") }} Todos los derechos reservados</p>
                </div>
            </div>
        </div>
    </footer>
{% endblock %}
</body>
</html>
