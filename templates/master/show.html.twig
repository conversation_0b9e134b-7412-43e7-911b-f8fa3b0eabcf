{% extends 'base-master.html.twig' %}

{% block title %}Aplicaciones{% endblock %}


{% block body %}
    <div>
        {{ form_start(form, {'attr': {'enctype': 'multipart/form-data', 'class': 'formulario'}}) }}


        <div class="nav-select">
            <div class="nav-container">
                {{ form_row(form.dominio, {
                    attr: {'class': 'select-drop'},
                    label_attr: {'class': 'select-title'}
                }) }}
            </div>
            <div class="nav-container">
                {{ form_row(form.databaseName, {
                    attr: {'class': 'select-drop'},
                    label_attr: {'class': 'select-title'}
                }) }}
            </div>
        </div>

        <div class="modulos-form form">
            <fieldset class="modulos-fieldset">
                <legend>Módulos</legend>

                <div class="fieldset-item">
                    <label class="choice-label">Benefits</label>
                    <div class="choice-group">
                        {{ form_widget(form.beneficios) }}
                    </div>
                </div>

                <div class="fieldset-item">
                    <label class="choice-label">Forms</label>
                    <div class="choice-group">
                        {{ form_widget(form.formularios) }}
                    </div>
                </div>

                <div class="fieldset-item">
                    <label class="choice-label">Events</label>
                    <div class="choice-group">
                        {{ form_widget(form.eventos) }}
                    </div>
                </div>

                <div class="fieldset-item">
                    <label class="choice-label">Check-in</label>
                    <div class="choice-group">
                        {{ form_widget(form.checador) }}
                    </div>
                </div>

                <div class="fieldset-item">
                    <label class="choice-label">Chat</label>
                    <div class="choice-group">
                        {{ form_widget(form.chat) }}
                    </div>
                </div>
            </fieldset>
        </div>

        <div class="form-2 form">
            <fieldset class="fieldset-2">
                <legend>Aviso de privacidad</legend>
                <label class="file" for="aviso">Archivo</label>
                {{ form_widget(form.aviso, {
                    attr: {'class': 'file-input', 'id': 'aviso'}
                }) }}
            </fieldset>
        </div>

        <div class="form-2 form">
            <fieldset class="fieldset-2">
                <legend>Logo</legend>
                <label class="file" for="logo">Archivo</label>
                {{ form_widget(form.logo, {
                    attr: {'class': 'file-input', 'id': 'logo'}
                }) }}
            </fieldset>
        </div>

        <div class="container-button">
            <a style="margin-right: 0.5rem; text-decoration: none"
               class="modulos-button"
               href="{{ path('app_master', {'dominio': 'Master'}) }}">Regresar</a>

            <button class="modulos-button">Enviar</button>
        </div>

        {{ form_end(form) }}
    </div>
{% endblock %}
