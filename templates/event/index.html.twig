{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Eventos{% endblock %}

{% block body %}
    <section class="header-sntiasg-r">
        <div class="container-fluid container-header">
            <h1 class="title-sntiasg">EVENTOS</h1>
        </div>
    </section>
    
    <section class="d-flex justify-content-center my-5">
        <div class="row container calendar-movil justify-content-between">
            <div class="col-8 calendar-container">
                <div id='calendar'></div>
            </div>
            <div class="col-3 list-sntiasg">
                <div class="list-container">
                    <div class="list-title-cont m-0">
                        <h4 class="list-title">EVENTOS PRÓXIMOS</h4>
                    </div>
                    <div>
                        {% set hayEventos = false %}
                        <ul id="eventList" class="list-group">
                            {% for event in events %}
                                {% if event.startDate|date('m') == "now"|date('m') and event.startDate|date('Y') == "now"|date('Y') %}
                                    {% set hayEventos = true %}
                                    <li class="list-group-item">
                                        <a href="{{ path('app_event_show', {'id': event.id, 'dominio': dominio}) }}">
                                            {{ event.title }}<br>
                                            {{ event.startDate ? event.startDate|date('j \\D\\E F')|upper : '' }}
                                        </a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if not hayEventos %}
                                <li class="list-group-item">No se encontraron eventos.</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
                <div class="add-event-container mt-4 d-flex justify-content-center">
                    <a href="{{ path('app_event_new', {'dominio': dominio}) }}" class="btn-y">CREAR EVENTO</a>
                </div>
            </div>
        </div>
    </section>

{% endblock %}

{% block javascripts %}
    {{ parent() }}

    <script>
        window.dominio = {{ (dominio ? "'" ~ dominio ~ "'" : "null") | raw }};
        window.calendarEvents = [
            {% for event in events %}
                {
                    id: '{{ event.id }}', 
                    title: '{{ event.title|e('js') }}',
                    start: '{{ event.startDate ? event.startDate|date('Y-m-d') : '' }}',
                    end: '{{ event.endDate ? event.endDate|date_modify('+1 day')|date('Y-m-d') : '' }}',
                },
            {% endfor %}
        ];
    </script>
    
{% endblock %}
