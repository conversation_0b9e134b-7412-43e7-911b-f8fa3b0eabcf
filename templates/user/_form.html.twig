{{ form_start(form, {
    'action':
        user is defined and user.id is defined and user.id
            ? path(app.request.get('_route'), {'dominio': dominio, 'id': user.id})
            : path(app.request.get('_route'), {'dominio': dominio})
}) }}

  {# SECCIÓN: FOTO DE PERFIL #}
  {% if form.photo is defined %}
    <div class="row mb-4">
      <div class="col-md-12">
        {{ form_row(form.photo) }}
      </div>
    </div>
  {% endif %}

  {# SECCIÓN: INFORMACIÓN PERSONAL #}
  <div class="row mb-3">
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.name) }}
    </div>
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.last_name) }}
    </div>
  </div>

  <div class="row mb-3">
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.curp) }}
    </div>
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.birthday) }}
    </div>
  </div>

  <div class="row mb-3">
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.gender) }}
    </div>
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.education) }}
    </div>
  </div>

  {# SECCIÓN: INFORMACIÓN DE CONTACTO #}
  <div class="row mb-3">
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.phone_number) }}
    </div>
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_label(form.email) }}
      {{ form_widget(form.email) }}
      {% for error in form.email.vars.errors %}
        <small class="text-danger-email mt-1">
          {{ error.message }}
        </small>
      {% endfor %}
    </div>
  </div>

  {# SECCIÓN: INFORMACIÓN LABORAL #}
  <div class="row mb-3">
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.company) }}
    </div>
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.employee_number) }}
    </div>
  </div>

  {# SECCIÓN: ACCESO Y PERMISOS #}
  <div class="row mb-3">
    {% if form.role is defined %}
      <div class="col-md-6 col-movil margin-form-sntiasg">
        {{ form_row(form.role) }}
      </div>
    {% endif %}
    {% if form.password is defined %}
      <div class="col-md-6 col-movil margin-form-sntiasg">
        {{ form_row(form.password) }}
      </div>
    {% endif %}
  </div>

  {# SECCIÓN: REGIONES #}
  {% if form.regions is defined %}
    <div class="row mb-4">
      <div class="col-md-12 margin-form-sntiasg">
        {{ form_row(form.regions) }}
      </div>
    </div>
  {% endif %}

  {# BOTÓN DE ENVÍO #}
  <div class="d-flex justify-content-center col-12 text-center mt-4">
    <button class="btn-y px-5">{{ button_label|default('CREAR') }}</button>
  </div>

{{ form_end(form) }}

{# Replace all path/url calls using dominio with app.request.attributes.get('dominio') #}
