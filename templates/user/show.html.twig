{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Detalle del Agremiado{% endblock %}

{% block body %}
  <div class="d-flex align-items-center justify-content-center min-vh-100">
    <div class="container modal-a container-show position-relative text-white rounded-4 shadow-lg">
      <a href="{{ path('app_user_index', {'dominio': dominio}) }}" class="btn-close btn-close-white position-absolute top-0 end-0 m-3"></a>

      <div class="row user-information g-4">
        <div class="col-md-4 user-photo d-flex justify-content-center align-items-center">
          {% if user.photo %}
            <div
              data-bs-toggle="modal"
              data-bs-target="#photoModal-{{ user.id }}"
              class="profile-picture d-flex align-items-center justify-content-center"
            >
              <img
                src="{{ image_full_url(user.photo) }}"
                alt="Foto de usuario"
                class="img-profile-picture img-fluid"
                style="object-fit: cover; width: 100%; height: 100%;"
              >
            </div>

            <div class="modal fade" id="photoModal-{{ user.id }}" tabindex="-1" aria-labelledby="photoModalLabel-{{ user.id }}" aria-hidden="true">
              <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content border-0 bg-transparent">
                  <div class="modal-body p-0 d-flex justify-content-center align-items-center">
                    <img
                      src="{{ image_full_url(user.photo) }}"
                      alt="Foto del usuario ampliada"
                      class="img-fluid rounded-4 shadow-lg"
                      style="max-height: 90vh; max-width: 100%; object-fit: contain;"
                    >
                  </div>
                </div>
              </div>
            </div>

          {% else %}
            <div
              class="d-flex align-items-center justify-content-center"
              style="width: 200px; height: 200px; background-color: #005A8B; border-radius: 50%;  box-shadow: 0 4px 12px rgba(0,0,0,0.2);"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 24 24" fill="#CCCCCC">
                <path d="M12 5c1.65 0 3 1.35 3 3s-1.35 3-3 3-3-1.35-3-3 1.35-3 3-3zm0 10c2.5 0 4.71 1.28 6 3.22-.03.52-.47.78-1 .78H7c-.53 0-.97-.26-1-.78C7.29 16.28 9.5 15 12 15z"/>
              </svg>
            </div>
          {% endif %}
        </div>

        <div class="col-md-8 user-data">
          <h2 class="fw-bold text-start modal-title-sntiasg mb-3">{{ user.name ~ ' ' ~ user.lastName }}</h2>
          <p class="modal-text-sntiasg"><strong>Teléfono:</strong> {{ user.phoneNumber }}</p>
          <p class="modal-text-sntiasg"><strong>Correo Electrónico:</strong> {{ user.email }}</p>
          <p class="modal-text-sntiasg"><strong>Fecha de Nacimiento:</strong> {{ user.birthday ? user.birthday|date('d/m/Y') : 'N/A' }}</p>
          <p class="modal-text-sntiasg"><strong>CURP:</strong> {{ user.curp }}</p>
          <p class="modal-text-sntiasg"><strong>Nº de Empleado:</strong> {{ user.employeeNumber }}</p>
          <p class="modal-text-sntiasg"><strong>Empresa:</strong> {{ user.company.name ?? 'N/A' }}</p>
          <p class="modal-text-sntiasg"><strong>Regiones:</strong>
            {% if user.regions|length > 0 %}
              {% for region in user.regions %}
                {{ region.name }}{% if not loop.last %}, {% endif %}
              {% endfor %}
            {% else %}
              N/A
            {% endif %}
          </p>
        </div>
      </div>

      <div class="row justify-content-center mt-4">
        <div class="col-3 edit-user text-center">
          <a href="{{ path('app_user_edit', {'id': user.id, 'dominio': dominio}) }}" class="btn-ye fw-bold shadow-sm">EDITAR AGREMIADO</a>
        </div>
      </div>

      <div class="container">
        <h5 class="modal-subtitle-sntiasg text-start mt-5 mb-3">Beneficiarios:</h5>

        <div class="overflow-auto">
          <div class="d-flex flex-wrap justify-content-center gap-3 pb-2">
            {% for beneficiary in beneficiaries %}
              <div class="card-beneficiary d-flex flex-column align-items-center text-center p-3" style="width: 20rem;">

                <div
                  class="overflow-hidden rounded-circle mb-2 d-flex justify-content-center align-items-center"
                  style="width: 80px; height: 80px; cursor: {% if beneficiary.photo %}pointer{% else %}default{% endif %};"
                  {% if beneficiary.photo %}
                    data-bs-toggle="modal"
                    data-bs-target="#photoModalBeneficiary-{{ beneficiary.id }}"
                  {% endif %}
                >
                  {% if beneficiary.photo %}
                    <img
                      src="{{ image_full_url(beneficiary.photo) }}"
                      alt="Foto del beneficiario"
                      style="width: 100%; height: 100%; object-fit: cover;"
                    />
                  {% else %}
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 146 145" fill="none"
                        style="width: 100%; height: 100%; object-fit: cover;">
                      <ellipse cx="73" cy="72.5" rx="73" ry="72.5" fill="white"/>
                      <path d="M62.2558 48.5356H46.1834C40.7094 48.5356 37.9704 48.5356 35.8796 49.5937C34.0405 50.5243 32.5463 52.0082 31.6093 53.8347C30.5439 55.9112 30.5439 58.6315 30.5439 64.068V91.2479C30.5439 96.6844 30.5439 99.3988 31.6093 101.475C32.5463 103.302 34.0405 104.791 35.8796 105.721C37.9683 106.778 40.7041 106.778 46.1673 106.778H102.887C108.35 106.778 111.082 106.778 113.171 105.721C115.01 104.791 116.509 103.302 117.446 101.475C118.51 99.4009 118.51 96.6878 118.51 91.2619V64.052C118.51 58.6262 118.51 55.9092 117.446 53.8347C116.509 52.0082 115.01 50.5243 113.171 49.5937C111.08 48.5356 108.347 48.5356 102.873 48.5356H86.7977M62.2558 48.5356H62.5578M62.2558 48.5356C62.317 48.5357 62.3814 48.5357 62.4493 48.5357L62.5578 48.5356M62.2558 48.5356C61.7372 48.5354 61.4466 48.5328 61.2167 48.5072C58.3452 48.1879 56.3757 45.4734 56.9788 42.6669C57.0349 42.4057 57.1458 42.0753 57.3654 41.4209L57.3749 41.3932C57.6257 40.6458 57.7512 40.2721 57.8897 39.9424C59.3083 36.5662 62.5208 34.2702 66.1944 34.0012C66.5533 33.975 66.9475 33.975 67.7407 33.975H81.3133C82.1065 33.975 82.504 33.975 82.8628 34.0012C86.5364 34.2702 89.7455 36.5662 91.1641 39.9424C91.3027 40.2721 91.4287 40.6454 91.6795 41.3928C91.9054 42.0658 92.0184 42.4025 92.0754 42.6674C92.6784 45.4739 90.7121 48.1879 87.8405 48.5072C87.6107 48.5328 87.3172 48.5354 86.7977 48.5356M62.5578 48.5356H86.4954M86.4954 48.5356H86.7977M86.4954 48.5356L86.604 48.5357C86.6719 48.5357 86.7363 48.5357 86.7977 48.5356M74.5272 92.2177C66.4301 92.2177 59.8661 85.6986 59.8661 77.657C59.8661 69.6153 66.4301 63.0963 74.5272 63.0963C82.6243 63.0963 89.1883 69.6153 89.1883 77.657C89.1883 85.6986 82.6243 92.2177 74.5272 92.2177Z" stroke="#5A5A5B" stroke-opacity="0.52" stroke-width="8" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  {% endif %}
                </div>

                <p class="mb-0 fw-bold">{{ beneficiary.name ~ ' ' ~ beneficiary.lastName }}</p>
                <small>{{ beneficiary.kinship }}</small>
                <small>{{ beneficiary.birthday ? beneficiary.birthday|date('d/m/Y') : 'N/A' }}</small>

                <div class="d-flex justify-content-center mt-3 gap-2 w-100">
                  <a href="{{ path('app_beneficiary_edit', {'id': beneficiary.id, 'dominio': dominio}) }}" class="btn-edit-beneficiary btn-sm rounded-pill px-3">Editar</a>

                  <button type="button" class="btn-delete-beneficiary btn-sm rounded-pill px-3" data-bs-toggle="modal" data-bs-target="#modalDeleteNotification-{{ beneficiary.id }}" data-form-id="delete-form-{{ beneficiary.id }}">Eliminar</button>
                </div>
              </div>

              <form id="delete-form-{{ beneficiary.id }}" method="post" action="{{ path('app_beneficiary_delete', {'id': beneficiary.id, 'dominio': dominio}) }}">
                <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ beneficiary.id) }}">
              </form>

              <div class="modal fade" id="modalDeleteNotification-{{ beneficiary.id }}" tabindex="-1" aria-labelledby="modalDeleteNotificationLabel-{{ beneficiary.id }}" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-md">
                  <div class="modal-content text-center p-3">
                    <div class="modal-body modal-delete">
                      <h5 class="modal-title-sntiasg">¿Deseas eliminar este beneficiario?</h5>
                      <div class="d-flex justify-content-center gap-2 mt-3">
                        <button type="button" class="btn-w" onclick="document.getElementById('delete-form-{{ beneficiary.id }}').submit();">ELIMINAR</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            {% else %}
              <p class="text-center text-white w-100">No hay beneficiarios registrados.</p>
            {% endfor %}
          </div>
        </div>

        <div class="row justify-content-center mt-5">
          <div class="col-3 add-beneficiary text-center">
            <a href="{{ path('app_beneficiary_new', {'user_id': user.id, 'dominio': dominio}) }}" class="btn-vr">AÑADIR BENEFICIARIO</a>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block javascripts %}
  {{ parent() }}
{% endblock %}
