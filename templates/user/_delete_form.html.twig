{% set dominio = app.request.attributes.get('dominio') %}

<button type="button" class="btn-r" data-bs-toggle="modal" data-bs-target="#modalDeleteUser" data-form-id="delete-form-{{ user.id }}">ELIMINAR</button>

<form id="delete-form-{{ user.id }}" method="post" action="{{ path('app_user_delete', {'id': user.id, 'dominio': dominio}) }}" onsubmit="return confirm('¿Estás seguro que deseas eliminar este usuario?');">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ user.id) }}">
</form>

<div class="modal fade" id="modalDeleteUser" tabindex="-1" aria-labelledby="modalDeleteUserLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content border-0 shadow-lg" style="border-radius: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="modal-body text-center p-4">
                <!-- Icono de usuario -->
                <div class="mb-3">
                    <i class="fas fa-user-times text-warning" style="font-size: 3rem;"></i>
                </div>

                <!-- Título elegante -->
                <h5 class="text-white fw-bold mb-3" style="font-size: 1.3rem;">
                    Eliminar Agremiado
                </h5>

                <!-- Texto conciso -->
                <p class="text-white-50 mb-4" style="font-size: 0.95rem;">
                    ¿Está seguro de eliminar este agremiado?
                </p>

                <!-- Botones modernos -->
                <div class="d-flex gap-2 justify-content-center">
                    <button type="button" class="btn btn-light btn-sm px-4" data-bs-dismiss="modal" style="border-radius: 25px;">
                        <i class="fas fa-times me-1"></i> Cancelar
                    </button>
                    <button id="btnConfirmDelete" type="button" class="btn btn-danger btn-sm px-4" style="border-radius: 25px;">
                        <i class="fas fa-user-times me-1"></i> Eliminar
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        let formToSubmit = null;

        document.querySelectorAll('.btn-r').forEach(button => {
            button.addEventListener('click', function () {
                const formId = button.getAttribute('data-form-id');
                formToSubmit = document.getElementById(formId);
            });
        });

        const confirmBtn = document.getElementById('btnConfirmDelete');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', function () {
                if (formToSubmit) {
                    formToSubmit.submit();
                    formToSubmit = null;
                }
            });
        }
    });
</script>
