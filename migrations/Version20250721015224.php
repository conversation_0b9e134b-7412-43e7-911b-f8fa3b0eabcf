<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250721015224 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE Tenant (id INT AUTO_INCREMENT NOT NULL, beneficios VARCHAR(1) NOT NULL, formularios VARCHAR(1) NOT NULL, eventos VARCHAR(1) NOT NULL, checador VARCHAR(1) NOT NULL, aviso LONGTEXT DEFAULT NULL, logo LONGTEXT DEFAULT NULL, dominio VARCHAR(50) NOT NULL, status VARCHAR(2) NOT NULL, databaseName VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE Tenant');
    }
}
